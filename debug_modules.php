<?php
// 调试新增模块
echo "<h2>模块调试信息</h2>";

// 检查模块文件是否存在
$modules = ['pending', 'rejected', 'vip_list', 'blacklist', 'pending_detail', 'rejected_detail', 'vip_detail', 'blacklist_detail'];

echo "<h3>1. 模块文件检查</h3>";
foreach ($modules as $mod) {
    $file = "module/{$mod}.php";
    $template = "themes/default/{$mod}.html";
    
    echo "<p><strong>{$mod}</strong>: ";
    echo "模块文件 " . (file_exists($file) ? "✓存在" : "✗不存在");
    echo " | 模板文件 " . (file_exists($template) ? "✓存在" : "✗不存在");
    echo "</p>";
}

// 检查URL参数
echo "<h3>2. URL参数检查</h3>";
echo "<p>当前mod参数: " . ($_GET['mod'] ?? '未设置') . "</p>";
echo "<p>REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>QUERY_STRING: " . $_SERVER['QUERY_STRING'] . "</p>";

// 检查index.php中的模块数组
echo "<h3>3. 模块数组检查</h3>";
$modarr = array('index', 'webdir', 'article', 'weblink', 'category', 'update', 'archives', 'search', 'siteinfo', 'artinfo', 'linkinfo', 'top', 'feedback', 'diypage', 'rssfeed', 'sitemap', 'ajaxget', 'getdata', 'api', 'pending', 'pending_detail', 'addurl', 'quicksubmit', 'blacklist', 'blacklist_detail', 'rejected', 'rejected_detail', 'vip_detail', 'vip_list', 'datastats');

foreach ($modules as $mod) {
    echo "<p><strong>{$mod}</strong>: " . (in_array($mod, $modarr) ? "✓在数组中" : "✗不在数组中") . "</p>";
}

// 测试链接
echo "<h3>4. 测试链接</h3>";
foreach ($modules as $mod) {
    echo "<p><a href='?mod={$mod}' target='_blank'>{$mod} (查询参数)</a></p>";
    echo "<p><a href='/{$mod}' target='_blank'>{$mod} (伪静态)</a></p>";
}

// 检查数据库连接（如果可能）
echo "<h3>5. 数据库状态检查</h3>";
if (file_exists('config.php')) {
    try {
        include_once 'config.php';
        if (isset($DB)) {
            echo "<p>数据库连接: ✓正常</p>";
            
            // 检查各状态的网站数量
            $statuses = [
                1 => '正常',
                2 => '待审核', 
                3 => 'VIP',
                4 => '审核不通过'
            ];
            
            foreach ($statuses as $status => $name) {
                $sql = "SELECT COUNT(*) as count FROM " . $DB->table('websites') . " WHERE web_status = $status";
                $result = $DB->fetch_one($sql);
                echo "<p>状态 {$status} ({$name}): {$result['count']} 个网站</p>";
            }
        } else {
            echo "<p>数据库连接: ✗未初始化</p>";
        }
    } catch (Exception $e) {
        echo "<p>数据库连接: ✗错误 - " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>配置文件: ✗不存在</p>";
}
?>
