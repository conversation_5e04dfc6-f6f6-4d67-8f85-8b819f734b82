<?php
/**
 * 修复自动评论配置问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>修复自动评论配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 修复自动评论配置</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    $options_table = $DB->table('options');
    
    // 步骤1：删除可能存在的重复配置
    echo "<h2>步骤 1: 清理重复配置</h2>";
    
    $auto_comment_options = array(
        'auto_comment_enabled',
        'auto_comment_count',
        'auto_comment_delay',
        'auto_comment_min_delay',
        'auto_comment_max_delay'
    );
    
    foreach ($auto_comment_options as $option_name) {
        $DB->query("DELETE FROM $options_table WHERE option_name = '$option_name'");
        echo "<div class='info'>清理配置: {$option_name}</div>";
    }
    
    // 步骤2：重新插入正确的配置
    echo "<h2>步骤 2: 插入正确配置</h2>";
    
    $config_data = array(
        'auto_comment_enabled' => 'yes',
        'auto_comment_count' => '5',
        'auto_comment_delay' => 'yes',
        'auto_comment_min_delay' => '1',
        'auto_comment_max_delay' => '30'
    );
    
    $success_count = 0;
    foreach ($config_data as $option_name => $option_value) {
        $result = $DB->insert($options_table, array(
            'option_name' => $option_name,
            'option_value' => $option_value
        ));
        
        if ($result) {
            echo "<div class='success'>✅ 添加配置: {$option_name} = {$option_value}</div>";
            $success_count++;
        } else {
            echo "<div class='error'>❌ 添加失败: {$option_name}</div>";
        }
    }
    
    // 步骤3：清理缓存
    echo "<h2>步骤 3: 清理缓存</h2>";
    
    // 清理可能的缓存文件
    $cache_dirs = array(
        ROOT_PATH . 'data/cache/',
        ROOT_PATH . 'data/compile/'
    );
    
    foreach ($cache_dirs as $cache_dir) {
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            $deleted = 0;
            foreach ($files as $file) {
                if (is_file($file) && unlink($file)) {
                    $deleted++;
                }
            }
            echo "<div class='info'>清理缓存目录: {$cache_dir} ({$deleted} 个文件)</div>";
        }
    }
    
    // 步骤4：验证配置
    echo "<h2>步骤 4: 验证配置</h2>";
    
    $verification_passed = true;
    foreach ($config_data as $option_name => $expected_value) {
        $current_value = $DB->fetch_one("SELECT option_value FROM $options_table WHERE option_name = '$option_name'");
        
        if ($current_value && $current_value['option_value'] == $expected_value) {
            echo "<div class='success'>✅ {$option_name}: {$current_value['option_value']}</div>";
        } else {
            echo "<div class='error'>❌ {$option_name}: 验证失败</div>";
            $verification_passed = false;
        }
    }
    
    // 步骤5：重新加载配置测试
    echo "<h2>步骤 5: 测试配置加载</h2>";
    
    // 强制重新加载配置
    unset($options);
    
    // 重新获取所有配置
    $query = $DB->query("SELECT option_name, option_value FROM $options_table");
    $options = array();
    while ($row = $DB->fetch_array($query)) {
        $options[$row['option_name']] = $row['option_value'];
    }
    
    // 测试自动评论配置函数
    if (file_exists(ROOT_PATH.'module/auto_comments.php')) {
        require_once(ROOT_PATH.'module/auto_comments.php');
        
        if (function_exists('get_auto_comment_config')) {
            $config = get_auto_comment_config();
            echo "<div class='success'>✅ 配置函数正常工作</div>";
            echo "<div class='info'>";
            echo "功能状态: " . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "评论数量: {$config['comment_count']} 条<br>";
            echo "延迟发布: " . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "延迟范围: {$config['min_delay']}-{$config['max_delay']} 分钟<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>❌ 配置函数不存在</div>";
            $verification_passed = false;
        }
    } else {
        echo "<div class='error'>❌ 自动评论模块文件不存在</div>";
        $verification_passed = false;
    }
    
    // 最终结果
    echo "<h2>修复结果</h2>";
    
    if ($verification_passed && $success_count == count($config_data)) {
        echo "<div class='success'>";
        echo "<h3>🎉 修复成功！</h3>";
        echo "<p>所有配置选项已正确设置，现在可以正常使用自动评论功能了。</p>";
        echo "<ul>";
        echo "<li>✅ 配置选项已添加到数据库</li>";
        echo "<li>✅ 缓存已清理</li>";
        echo "<li>✅ 配置加载正常</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='system/option.php?opt=comment' class='btn'>前往后台配置</a>";
        echo "<a href='test_auto_comments.php' class='btn'>测试功能</a>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 修复未完全成功</h3>";
        echo "<p>请检查以下问题：</p>";
        echo "<ul>";
        echo "<li>数据库权限是否正确</li>";
        echo "<li>文件权限是否正确</li>";
        echo "<li>是否有其他错误信息</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='install_auto_comments.php' class='btn'>重新安装</a>";
        echo "<a href='check_auto_comment_config.php' class='btn'>详细检查</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 修复失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
