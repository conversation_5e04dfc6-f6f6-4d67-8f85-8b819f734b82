<?php
/**
 * 自动评论功能演示脚本
 * 展示完整的功能流程和效果
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');
require_once(ROOT_PATH.'module/auto_comments.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>自动评论功能演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .demo-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        .comment-preview { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 3px solid #007bff; border-radius: 4px; }
        .user-info { background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 4px; font-size: 14px; }
        .rating-stars { color: #ffc107; font-size: 16px; }
        .feature-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { background: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; }
        .feature-card h3 { margin-top: 0; color: #007bff; }
        .workflow { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .workflow h3 { margin-top: 0; }
        .step { background: rgba(255,255,255,0.1); padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🚀 自动评论功能演示</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 功能特性展示
    echo "<div class='demo-section'>";
    echo "<h2>🎯 功能特性</h2>";
    echo "<div class='feature-list'>";
    
    echo "<div class='feature-card'>";
    echo "<h3>🤖 智能触发</h3>";
    echo "<p>当网站从待审核状态变为已审核时，系统自动检测并触发评论添加功能，无需人工干预。</p>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h3>🎲 随机内容</h3>";
    echo "<p>从25条预设评论模板中随机选择，确保每次添加的评论内容都不相同，增加真实性。</p>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h3>👥 智能用户</h3>";
    echo "<p>优先使用现有注册会员作为评论者，不足时自动使用匿名用户，保证评论的多样性。</p>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h3>⭐ 高质量评分</h3>";
    echo "<p>自动生成4-5星的高质量评分，偶尔3星，让评分更加真实可信。</p>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h3>⏰ 时间分散</h3>";
    echo "<p>支持1-30分钟的随机时间间隔发布，避免所有评论同时出现。</p>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h3>🛠️ 灵活配置</h3>";
    echo "<p>支持开关控制、数量设置、延迟配置等多种参数，满足不同需求。</p>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // 工作流程展示
    echo "<div class='workflow'>";
    echo "<h3>📋 工作流程</h3>";
    echo "<div class='step'>1. 用户提交网站 → 状态：待审核</div>";
    echo "<div class='step'>2. 管理员审核通过 → 状态：已审核</div>";
    echo "<div class='step'>3. 系统检测状态变化 → 触发自动评论</div>";
    echo "<div class='step'>4. 随机选择评论内容和用户 → 生成评论</div>";
    echo "<div class='step'>5. 按时间间隔发布评论 → 完成</div>";
    echo "</div>";
    
    // 配置状态展示
    echo "<div class='demo-section'>";
    echo "<h2>⚙️ 当前配置</h2>";
    
    $config = get_auto_comment_config();
    echo "<div class='info'>";
    echo "<strong>功能状态：</strong>" . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
    echo "<strong>评论数量：</strong>{$config['comment_count']} 条<br>";
    echo "<strong>延迟发布：</strong>" . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
    echo "<strong>延迟范围：</strong>{$config['min_delay']}-{$config['max_delay']} 分钟<br>";
    echo "</div>";
    echo "</div>";
    
    // 评论内容预览
    echo "<div class='demo-section'>";
    echo "<h2>📝 评论内容预览</h2>";
    
    $templates = get_random_comment_contents(5);
    echo "<p>以下是从评论模板库中随机选择的5条评论内容：</p>";
    
    foreach ($templates as $i => $content) {
        echo "<div class='comment-preview'>";
        echo "<strong>评论 " . ($i + 1) . "：</strong>" . htmlspecialchars($content);
        echo "</div>";
    }
    echo "</div>";
    
    // 用户选择预览
    echo "<div class='demo-section'>";
    echo "<h2>👤 评论用户预览</h2>";
    
    $users = get_random_comment_users(5);
    echo "<p>以下是系统随机选择的5个评论用户：</p>";
    
    foreach ($users as $i => $user) {
        $type = $user['is_anonymous'] ? '匿名用户' : '注册会员';
        $icon = $user['is_anonymous'] ? '👤' : '👨‍💼';
        echo "<div class='user-info'>";
        echo "{$icon} <strong>{$user['user_name']}</strong> ({$type})";
        if (!$user['is_anonymous']) {
            echo " - ID: {$user['user_id']}";
        }
        echo "</div>";
    }
    echo "</div>";
    
    // 评分预览
    echo "<div class='demo-section'>";
    echo "<h2>⭐ 评分预览</h2>";
    
    echo "<p>以下是系统生成的随机评分示例：</p>";
    for ($i = 1; $i <= 5; $i++) {
        $ratings = generate_random_ratings();
        echo "<div class='user-info'>";
        echo "评分示例 {$i}：";
        echo "<span class='rating-stars'>";
        echo "内容质量 " . str_repeat('★', $ratings['content_quality']) . str_repeat('☆', 5-$ratings['content_quality']) . " ";
        echo "服务质量 " . str_repeat('★', $ratings['service_quality']) . str_repeat('☆', 5-$ratings['service_quality']) . " ";
        echo "诚信度 " . str_repeat('★', $ratings['trust_level']) . str_repeat('☆', 5-$ratings['trust_level']);
        echo "</span>";
        echo "</div>";
    }
    echo "</div>";
    
    // 完整演示
    echo "<div class='demo-section'>";
    echo "<h2>🎬 完整演示</h2>";
    
    echo "<p>以下是一个完整的自动评论演示，展示实际添加的评论效果：</p>";
    
    // 模拟生成完整的评论
    $demo_comments = array();
    $demo_templates = get_random_comment_contents(3);
    $demo_users = get_random_comment_users(3);
    
    for ($i = 0; $i < 3; $i++) {
        $ratings = generate_random_ratings();
        $demo_comments[] = array(
            'user' => $demo_users[$i],
            'content' => $demo_templates[$i],
            'ratings' => $ratings,
            'time' => time() + ($i * 600) // 每10分钟一条
        );
    }
    
    foreach ($demo_comments as $i => $comment) {
        echo "<div class='comment-preview'>";
        echo "<div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;'>";
        echo "<strong>" . htmlspecialchars($comment['user']['user_name']) . "</strong>";
        echo "<small style='color: #666;'>" . date('Y-m-d H:i:s', $comment['time']) . "</small>";
        echo "</div>";
        
        echo "<div class='rating-stars' style='margin-bottom: 10px;'>";
        echo "内容质量 " . str_repeat('★', $comment['ratings']['content_quality']) . " ";
        echo "服务质量 " . str_repeat('★', $comment['ratings']['service_quality']) . " ";
        echo "诚信度 " . str_repeat('★', $comment['ratings']['trust_level']);
        echo "</div>";
        
        echo "<div>" . htmlspecialchars($comment['content']) . "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // 操作链接
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='install_auto_comments.php' class='btn'>安装功能</a>";
    echo "<a href='test_auto_comments.php' class='btn'>测试功能</a>";
    echo "<a href='system/option.php?opt=comment' class='btn'>配置设置</a>";
    echo "<a href='system/auto_comment_templates.php' class='btn'>管理模板</a>";
    echo "<a href='index.php' class='btn'>返回首页</a>";
    echo "</div>";
    
    echo "<div class='info' style='margin-top: 30px;'>";
    echo "<h3>💡 使用提示</h3>";
    echo "<ul>";
    echo "<li>首次使用请先运行安装脚本初始化功能</li>";
    echo "<li>可以在后台配置中调整评论数量和发布间隔</li>";
    echo "<li>建议定期更新评论模板内容以保持多样性</li>";
    echo "<li>功能会在网站审核通过时自动触发，无需手动操作</li>";
    echo "<li>测试完成后请删除演示和测试文件</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 演示失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
