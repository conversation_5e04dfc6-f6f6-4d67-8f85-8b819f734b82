<?php
/**
 * 检查自动评论配置选项
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>检查自动评论配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 检查自动评论配置</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 检查配置选项表
    $options_table = $DB->table('options');
    echo "<h2>1. 检查配置选项表</h2>";
    echo "<div class='info'>配置表名: {$options_table}</div>";
    
    // 检查自动评论相关配置
    $auto_comment_options = array(
        'auto_comment_enabled' => 'yes',
        'auto_comment_count' => '5',
        'auto_comment_delay' => 'yes',
        'auto_comment_min_delay' => '1',
        'auto_comment_max_delay' => '30'
    );
    
    echo "<h2>2. 检查配置选项</h2>";
    echo "<table>";
    echo "<tr><th>配置项</th><th>期望值</th><th>当前值</th><th>状态</th><th>操作</th></tr>";
    
    $missing_options = array();
    
    foreach ($auto_comment_options as $option_name => $expected_value) {
        $current_value = $DB->fetch_one("SELECT option_value FROM $options_table WHERE option_name = '$option_name'");
        
        if ($current_value) {
            $status = "<span style='color: green;'>✅ 存在</span>";
            $action = "无需操作";
            $value = $current_value['option_value'];
        } else {
            $status = "<span style='color: red;'>❌ 缺失</span>";
            $action = "<a href='?action=add&option={$option_name}&value={$expected_value}' class='btn' style='padding: 5px 10px; font-size: 12px;'>添加</a>";
            $value = "无";
            $missing_options[$option_name] = $expected_value;
        }
        
        echo "<tr>";
        echo "<td>{$option_name}</td>";
        echo "<td>{$expected_value}</td>";
        echo "<td>{$value}</td>";
        echo "<td>{$status}</td>";
        echo "<td>{$action}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // 处理添加配置的请求
    if (isset($_GET['action']) && $_GET['action'] == 'add' && isset($_GET['option']) && isset($_GET['value'])) {
        $option_name = $_GET['option'];
        $option_value = $_GET['value'];
        
        // 验证选项名是否在允许列表中
        if (array_key_exists($option_name, $auto_comment_options)) {
            $result = $DB->insert($options_table, array(
                'option_name' => $option_name,
                'option_value' => $option_value
            ));
            
            if ($result) {
                echo "<div class='success'>✅ 成功添加配置选项: {$option_name} = {$option_value}</div>";
                echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
            } else {
                echo "<div class='error'>❌ 添加配置选项失败: {$option_name}</div>";
            }
        }
    }
    
    // 批量添加缺失的配置
    if (!empty($missing_options)) {
        echo "<h2>3. 批量修复</h2>";
        echo "<div class='info'>发现 " . count($missing_options) . " 个缺失的配置选项</div>";
        echo "<a href='?action=fix_all' class='btn'>一键修复所有缺失配置</a>";
        
        if (isset($_GET['action']) && $_GET['action'] == 'fix_all') {
            $fixed_count = 0;
            foreach ($missing_options as $option_name => $option_value) {
                $result = $DB->insert($options_table, array(
                    'option_name' => $option_name,
                    'option_value' => $option_value
                ));
                
                if ($result) {
                    $fixed_count++;
                    echo "<div class='success'>✅ 添加: {$option_name} = {$option_value}</div>";
                } else {
                    echo "<div class='error'>❌ 失败: {$option_name}</div>";
                }
            }
            
            echo "<div class='success'>✅ 批量修复完成，成功添加 {$fixed_count} 个配置选项</div>";
            echo "<script>setTimeout(function(){ location.reload(); }, 3000);</script>";
        }
    } else {
        echo "<h2>3. 配置状态</h2>";
        echo "<div class='success'>✅ 所有配置选项都已正确设置</div>";
    }
    
    // 检查评论模板表
    echo "<h2>4. 检查评论模板表</h2>";
    $template_table = $DB->table('auto_comment_templates');
    
    $table_exists = $DB->query("SHOW TABLES LIKE '$template_table'");
    if ($DB->num_rows($table_exists)) {
        $template_count = $DB->get_count($template_table, '1');
        $active_count = $DB->get_count($template_table, 'status = 1');
        
        echo "<div class='success'>✅ 评论模板表存在</div>";
        echo "<div class='info'>总模板数: {$template_count} 条，启用: {$active_count} 条</div>";
    } else {
        echo "<div class='error'>❌ 评论模板表不存在</div>";
        echo "<a href='install_auto_comments.php' class='btn'>重新安装</a>";
    }
    
    // 检查当前配置值
    echo "<h2>5. 当前配置值</h2>";
    
    // 重新加载配置
    $current_options = array();
    $query = $DB->query("SELECT option_name, option_value FROM $options_table WHERE option_name LIKE 'auto_comment_%'");
    while ($row = $DB->fetch_array($query)) {
        $current_options[$row['option_name']] = $row['option_value'];
    }
    
    echo "<table>";
    echo "<tr><th>配置项</th><th>当前值</th><th>说明</th></tr>";
    echo "<tr><td>auto_comment_enabled</td><td>" . ($current_options['auto_comment_enabled'] ?? '未设置') . "</td><td>是否启用自动评论</td></tr>";
    echo "<tr><td>auto_comment_count</td><td>" . ($current_options['auto_comment_count'] ?? '未设置') . "</td><td>每次添加评论数量</td></tr>";
    echo "<tr><td>auto_comment_delay</td><td>" . ($current_options['auto_comment_delay'] ?? '未设置') . "</td><td>是否启用延迟发布</td></tr>";
    echo "<tr><td>auto_comment_min_delay</td><td>" . ($current_options['auto_comment_min_delay'] ?? '未设置') . "</td><td>最小延迟时间(分钟)</td></tr>";
    echo "<tr><td>auto_comment_max_delay</td><td>" . ($current_options['auto_comment_max_delay'] ?? '未设置') . "</td><td>最大延迟时间(分钟)</td></tr>";
    echo "</table>";
    
    // 测试配置加载
    echo "<h2>6. 测试配置加载</h2>";
    
    if (file_exists(ROOT_PATH.'module/auto_comments.php')) {
        require_once(ROOT_PATH.'module/auto_comments.php');
        
        if (function_exists('get_auto_comment_config')) {
            $config = get_auto_comment_config();
            echo "<div class='success'>✅ 配置函数正常工作</div>";
            echo "<div class='info'>";
            echo "功能状态: " . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "评论数量: {$config['comment_count']} 条<br>";
            echo "延迟发布: " . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "延迟范围: {$config['min_delay']}-{$config['max_delay']} 分钟<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>❌ 配置函数不存在</div>";
        }
    } else {
        echo "<div class='error'>❌ 自动评论模块文件不存在</div>";
    }
    
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='system/option.php?opt=comment' class='btn'>前往后台配置</a>";
    echo "<a href='test_auto_comments.php' class='btn'>测试功能</a>";
    echo "<a href='install_auto_comments.php' class='btn'>重新安装</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 检查失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
