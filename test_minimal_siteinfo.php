<?php
/**
 * 最小化网站详情页测试
 * 用于快速验证修复效果
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>最小化网站详情页测试</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

try {
    // 1. 包含初始化文件
    echo "<h2>1. 初始化系统</h2>";
    require_once('source/init.php');
    echo "<span class='success'>✓</span> 系统初始化成功<br>";
    
    // 2. 定义调试模式
    define('DEBUG', true);
    
    // 3. 模拟网站详情页请求
    echo "<h2>2. 模拟网站详情页请求</h2>";
    $_GET['mod'] = 'siteinfo';
    $_GET['wid'] = isset($_GET['wid']) ? intval($_GET['wid']) : 1;
    
    echo "<span class='info'>ℹ</span> 请求参数: mod=siteinfo, wid=" . $_GET['wid'] . "<br>";
    
    // 4. 包含网站详情页模块
    echo "<h2>3. 加载网站详情页模块</h2>";
    
    if (file_exists('module/siteinfo.php')) {
        echo "<span class='success'>✓</span> siteinfo.php 文件存在<br>";
        
        // 捕获输出
        ob_start();
        $error_occurred = false;
        
        try {
            include('module/siteinfo.php');
            echo "<span class='success'>✓</span> siteinfo.php 执行成功<br>";
        } catch (Exception $e) {
            $error_occurred = true;
            echo "<span class='error'>✗</span> siteinfo.php 执行失败: " . $e->getMessage() . "<br>";
        } catch (Error $e) {
            $error_occurred = true;
            echo "<span class='error'>✗</span> PHP错误: " . $e->getMessage() . "<br>";
        }
        
        $output = ob_get_contents();
        ob_end_clean();
        
        if (!$error_occurred && !empty($output)) {
            echo "<h2>4. 页面输出成功</h2>";
            echo "<span class='success'>✓</span> 网站详情页生成成功，输出长度: " . strlen($output) . " 字符<br>";
            
            // 检查输出内容
            if (strpos($output, '<html') !== false) {
                echo "<span class='success'>✓</span> 包含HTML标签<br>";
            }
            if (strpos($output, 'comments-list') !== false) {
                echo "<span class='success'>✓</span> 包含评论列表容器<br>";
            }
            if (strpos($output, 'loadComments') !== false) {
                echo "<span class='success'>✓</span> 包含评论加载函数<br>";
            }
            
            echo "<hr>";
            echo "<h3>实际页面输出预览（前500字符）:</h3>";
            echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;max-height:200px;overflow:auto;'>";
            echo htmlspecialchars(substr($output, 0, 500));
            if (strlen($output) > 500) {
                echo "\n... (输出被截断)";
            }
            echo "</pre>";
            
            echo "<hr>";
            echo "<p><strong>测试结果：</strong> <span class='success'>网站详情页修复成功！</span></p>";
            echo "<p><a href='?mod=siteinfo&wid=" . $_GET['wid'] . "' target='_blank'>在新窗口中查看完整页面</a></p>";
            
        } else if ($error_occurred) {
            echo "<h2>4. 执行失败</h2>";
            echo "<span class='error'>✗</span> 网站详情页执行过程中出现错误<br>";
            
            if (!empty($output)) {
                echo "<h3>错误输出:</h3>";
                echo "<pre style='background:#ffe6e6;padding:10px;border:1px solid #ff0000;'>";
                echo htmlspecialchars($output);
                echo "</pre>";
            }
        } else {
            echo "<h2>4. 无输出</h2>";
            echo "<span class='error'>✗</span> 网站详情页没有产生任何输出（可能是重定向或缓存问题）<br>";
        }
        
    } else {
        echo "<span class='error'>✗</span> siteinfo.php 文件不存在<br>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color:red;'>测试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<h2 style='color:red;'>PHP错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<h2>其他测试工具</h2>";
echo "<ul>";
echo "<li><a href='diagnose_siteinfo.php'>完整系统诊断</a></li>";
echo "<li><a href='test_siteinfo_simple.php'>详细测试页面</a></li>";
echo "<li><a href='enable_debug.php'>调试工具集合</a></li>";
echo "</ul>";

?>
