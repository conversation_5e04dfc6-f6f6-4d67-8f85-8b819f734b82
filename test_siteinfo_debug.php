<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>网站详情页调试</h2>";

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

echo "<h3>1. 基本信息</h3>";
echo "ROOT_PATH: " . ROOT_PATH . "<br/>";
echo "APP_PATH: " . APP_PATH . "<br/>";
echo "MOD_PATH: " . MOD_PATH . "<br/>";

// 检查关键文件
echo "<h3>2. 关键文件检查</h3>";
$files = array(
    'source/init.php' => '初始化文件',
    'module/siteinfo.php' => '网站详情模块',
    'themes/default/siteinfo.html' => '网站详情模板',
    'module/website_comments.php' => '评论模块',
    'module/comment_handler.php' => '评论处理器'
);

foreach ($files as $file => $desc) {
    if (file_exists($file)) {
        echo "✓ {$desc} 存在 ({$file})<br/>";
    } else {
        echo "✗ {$desc} 不存在 ({$file})<br/>";
    }
}

// 尝试加载初始化文件
echo "<h3>3. 初始化测试</h3>";
try {
    require(APP_PATH.'init.php');
    echo "✓ init.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ init.php 加载失败: " . $e->getMessage() . "<br/>";
    exit;
}

// 检查数据库连接
echo "<h3>4. 数据库连接测试</h3>";
try {
    if (isset($DB) && $DB) {
        echo "✓ 数据库连接成功<br/>";
        
        // 测试查询
        $test_query = "SELECT COUNT(*) as count FROM " . $DB->table('websites') . " LIMIT 1";
        $result = $DB->fetch_one($test_query);
        echo "✓ 数据库查询成功，网站总数: " . $result['count'] . "<br/>";
    } else {
        echo "✗ 数据库连接失败<br/>";
    }
} catch (Exception $e) {
    echo "✗ 数据库错误: " . $e->getMessage() . "<br/>";
}

// 检查评论相关功能
echo "<h3>5. 评论功能检查</h3>";
try {
    if (file_exists('module/website_comments.php')) {
        require_once('module/website_comments.php');
        echo "✓ 评论模块加载成功<br/>";
        
        // 测试违规检测函数
        if (function_exists('check_comment_violation')) {
            echo "✓ 违规检测函数存在<br/>";
            
            // 测试违规检测
            $test_result = check_comment_violation('这是一个测试评论');
            echo "✓ 违规检测测试: " . ($test_result ? '检测到违规' : '正常内容') . "<br/>";
        } else {
            echo "⚠ 违规检测函数不存在<br/>";
        }
    } else {
        echo "✗ 评论模块文件不存在<br/>";
    }
} catch (Exception $e) {
    echo "✗ 评论功能错误: " . $e->getMessage() . "<br/>";
}

// 检查模板文件语法
echo "<h3>6. 模板文件检查</h3>";
$template_file = 'themes/default/siteinfo.html';
if (file_exists($template_file)) {
    $content = file_get_contents($template_file);
    
    // 检查基本语法问题
    $issues = array();
    
    // 检查未闭合的标签
    $open_tags = substr_count($content, '<script');
    $close_tags = substr_count($content, '</script>');
    if ($open_tags != $close_tags) {
        $issues[] = "script标签不匹配 (开始: $open_tags, 结束: $close_tags)";
    }
    
    // 检查模板字符串语法
    if (strpos($content, '${window.isAdmin ? `') !== false) {
        $issues[] = "发现嵌套模板字符串语法错误";
    }
    
    if (empty($issues)) {
        echo "✓ 模板文件基本语法检查通过<br/>";
    } else {
        echo "⚠ 模板文件发现问题:<br/>";
        foreach ($issues as $issue) {
            echo "  - $issue<br/>";
        }
    }
} else {
    echo "✗ 模板文件不存在<br/>";
}

// 模拟网站详情页访问
echo "<h3>7. 模拟访问测试</h3>";
try {
    // 设置模拟参数
    $_GET['mod'] = 'siteinfo';
    $_GET['web_id'] = 1;
    
    echo "✓ 模拟参数设置完成<br/>";
    echo "访问URL: ?mod=siteinfo&web_id=1<br/>";
    
    // 检查是否有网站数据
    if (isset($DB)) {
        $web_data = $DB->fetch_one("SELECT web_id, web_name, web_url FROM " . $DB->table('websites') . " WHERE web_id = 1 LIMIT 1");
        if ($web_data) {
            echo "✓ 找到测试网站数据: " . $web_data['web_name'] . "<br/>";
        } else {
            echo "⚠ 未找到ID为1的网站数据<br/>";
        }
    }
    
} catch (Exception $e) {
    echo "✗ 模拟访问错误: " . $e->getMessage() . "<br/>";
}

echo "<h3>8. 建议操作</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px;'>";
echo "<strong>如果网站详情页仍然白屏，请尝试以下操作：</strong><br/>";
echo "1. <a href='?mod=siteinfo&web_id=1' target='_blank'>直接访问网站详情页</a><br/>";
echo "2. 检查浏览器开发者工具的控制台错误<br/>";
echo "3. 检查服务器错误日志<br/>";
echo "4. 临时禁用评论功能进行测试<br/>";
echo "5. 恢复模板文件的备份版本<br/>";
echo "</div>";

echo "<h3>9. 快速修复</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
echo "<strong>如果确认是评论功能导致的问题，可以：</strong><br/>";
echo "1. 临时注释掉评论相关的JavaScript代码<br/>";
echo "2. 移除违规检测功能<br/>";
echo "3. 使用备份的模板文件<br/>";
echo "</div>";
?>
