<?php
/**
 * 搜索引擎推送功能测试文件
 * 用于测试推送功能是否正常工作
 */

// 设置基本常量
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(APP_PATH.'init.php');
require_once(APP_PATH.'module/search_push.php');

// 加载网站配置
define('IN_HANFOX', true); // 兼容配置文件的安全检查
require(ROOT_PATH.'data/static/options.php');
$options = $static_data;

echo "<h2>搜索引擎推送功能测试</h2>";

// 测试1：检查函数是否存在
echo "<h3>1. 函数检查</h3>";
$functions = array(
    'get_all_urls',
    'push_to_baidu', 
    'push_to_google',
    'push_to_bing',
    'generate_sitemap_xml',
    'get_push_config',
    'save_push_config',
    'log_push_result',
    'get_push_logs'
);

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✓ 函数 {$func} 存在<br/>";
    } else {
        echo "✗ 函数 {$func} 不存在<br/>";
    }
}

// 测试2：获取URL列表
echo "<h3>2. URL列表测试</h3>";
try {
    $urls = get_all_urls();
    echo "✓ 成功获取 " . count($urls) . " 个URL<br/>";
    echo "前5个URL：<br/>";
    for ($i = 0; $i < min(5, count($urls)); $i++) {
        echo "- " . htmlspecialchars($urls[$i]) . "<br/>";
    }
} catch (Exception $e) {
    echo "✗ 获取URL失败：" . $e->getMessage() . "<br/>";
}

// 测试3：配置功能
echo "<h3>3. 配置功能测试</h3>";
try {
    $config = get_push_config();
    echo "✓ 成功获取推送配置<br/>";
    echo "百度Token：" . (empty($config['baidu_token']) ? '未配置' : '已配置') . "<br/>";
    echo "谷歌API密钥：" . (empty($config['google_key']) ? '未配置' : '已配置') . "<br/>";
    echo "必应API密钥：" . (empty($config['bing_key']) ? '未配置' : '已配置') . "<br/>";
} catch (Exception $e) {
    echo "✗ 获取配置失败：" . $e->getMessage() . "<br/>";
}

// 测试4：网站地图生成
echo "<h3>4. 网站地图生成测试</h3>";
try {
    $xml = generate_sitemap_xml();
    if (file_exists(ROOT_PATH . 'sitemap.xml')) {
        echo "✓ 网站地图生成成功<br/>";
        echo "文件大小：" . filesize(ROOT_PATH . 'sitemap.xml') . " 字节<br/>";
        echo "访问地址：<a href='" . $options['site_url'] . "sitemap.xml' target='_blank'>" . $options['site_url'] . "sitemap.xml</a><br/>";
    } else {
        echo "✗ 网站地图文件未生成<br/>";
    }
} catch (Exception $e) {
    echo "✗ 网站地图生成失败：" . $e->getMessage() . "<br/>";
}

// 测试5：数据库连接
echo "<h3>5. 数据库连接测试</h3>";
try {
    $table = $DB->table('options');
    $result = $DB->get_one("SELECT COUNT(*) as count FROM $table");
    echo "✓ 数据库连接正常，options表有 " . $result['count'] . " 条记录<br/>";
} catch (Exception $e) {
    echo "✗ 数据库连接失败：" . $e->getMessage() . "<br/>";
}

// 测试6：模板文件检查
echo "<h3>6. 模板文件检查</h3>";
$template_file = ROOT_PATH . 'themes/system/search_push.html';
if (file_exists($template_file)) {
    echo "✓ 模板文件存在<br/>";
    echo "文件大小：" . filesize($template_file) . " 字节<br/>";
} else {
    echo "✗ 模板文件不存在<br/>";
}

// 测试7：后台文件检查
echo "<h3>7. 后台文件检查</h3>";
$admin_file = ROOT_PATH . 'system/search_push.php';
if (file_exists($admin_file)) {
    echo "✓ 后台管理文件存在<br/>";
    echo "文件大小：" . filesize($admin_file) . " 字节<br/>";
} else {
    echo "✗ 后台管理文件不存在<br/>";
}

echo "<h3>测试完成</h3>";
echo "<p>如果所有测试都通过，说明搜索引擎推送功能已正确安装。</p>";
echo "<p>您可以访问后台管理系统：<a href='system/search_push.php' target='_blank'>搜索引擎推送管理</a></p>";
?>
