<?php
/**
 * 网站详情页诊断脚本
 * 用于检查siteinfo页面白屏问题
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>网站详情页诊断</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .warning{color:orange;} .info{color:blue;}</style>";

// 1. 检查基础文件
echo "<h2>1. 基础文件检查</h2>";

$files_to_check = array(
    'source/init.php',
    'module/siteinfo.php', 
    'themes/default/siteinfo.html',
    'module/website_comments.php',
    'module/comment_handler.php'
);

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✓</span> $file 存在<br>";
    } else {
        echo "<span class='error'>✗</span> $file 不存在<br>";
    }
}

// 2. 尝试包含初始化文件
echo "<h2>2. 初始化文件测试</h2>";
try {
    if (file_exists('source/init.php')) {
        require_once('source/init.php');
        echo "<span class='success'>✓</span> init.php 加载成功<br>";
        
        // 检查关键变量
        if (isset($DB)) {
            echo "<span class='success'>✓</span> 数据库连接对象存在<br>";
        } else {
            echo "<span class='error'>✗</span> 数据库连接对象不存在<br>";
        }
        
        if (isset($smarty)) {
            echo "<span class='success'>✓</span> Smarty模板引擎存在<br>";
        } else {
            echo "<span class='error'>✗</span> Smarty模板引擎不存在<br>";
        }
        
        if (isset($options)) {
            echo "<span class='success'>✓</span> 系统配置存在<br>";
        } else {
            echo "<span class='error'>✗</span> 系统配置不存在<br>";
        }
        
    } else {
        echo "<span class='error'>✗</span> init.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 初始化失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "<span class='error'>✗</span> 致命错误: " . $e->getMessage() . "<br>";
}

// 3. 检查评论模块
echo "<h2>3. 评论模块检查</h2>";
try {
    if (file_exists('module/website_comments.php')) {
        require_once('module/website_comments.php');
        echo "<span class='success'>✓</span> website_comments.php 加载成功<br>";
        
        // 检查关键函数
        if (function_exists('get_website_comments')) {
            echo "<span class='success'>✓</span> get_website_comments 函数存在<br>";
        } else {
            echo "<span class='error'>✗</span> get_website_comments 函数不存在<br>";
        }
        
        if (function_exists('get_website_comment_stats')) {
            echo "<span class='success'>✓</span> get_website_comment_stats 函数存在<br>";
        } else {
            echo "<span class='error'>✗</span> get_website_comment_stats 函数不存在<br>";
        }
        
    } else {
        echo "<span class='error'>✗</span> website_comments.php 文件不存在<br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗</span> 评论模块加载失败: " . $e->getMessage() . "<br>";
}

// 4. 测试数据库连接
echo "<h2>4. 数据库连接测试</h2>";
if (isset($DB)) {
    try {
        // 测试简单查询
        $test_query = $DB->query("SELECT 1 as test");
        if ($test_query) {
            echo "<span class='success'>✓</span> 数据库连接正常<br>";
            $DB->free_result($test_query);
        } else {
            echo "<span class='error'>✗</span> 数据库查询失败<br>";
        }
        
        // 检查关键表是否存在
        $tables_to_check = array('websites', 'website_comments', 'users', 'categories');
        foreach ($tables_to_check as $table) {
            $table_name = $DB->table($table);
            $check_query = $DB->query("SHOW TABLES LIKE '$table_name'");
            if ($check_query && $DB->fetch_array($check_query)) {
                echo "<span class='success'>✓</span> 表 $table 存在<br>";
            } else {
                echo "<span class='error'>✗</span> 表 $table 不存在<br>";
            }
            if ($check_query) $DB->free_result($check_query);
        }
        
    } catch (Exception $e) {
        echo "<span class='error'>✗</span> 数据库测试失败: " . $e->getMessage() . "<br>";
    }
} else {
    echo "<span class='error'>✗</span> 数据库对象不存在<br>";
}

// 5. 测试网站详情页核心逻辑
echo "<h2>5. 网站详情页逻辑测试</h2>";

// 模拟一个网站ID进行测试
$test_web_id = 1;

if (isset($DB)) {
    try {
        // 检查是否有网站数据
        $websites_table = $DB->table('websites');
        $webdata_table = $DB->table('webdata');
        
        $test_website = $DB->fetch_one("SELECT w.*, d.* FROM $websites_table w LEFT JOIN $webdata_table d ON w.web_id = d.web_id WHERE w.web_status = 3 LIMIT 1");
        
        if ($test_website) {
            echo "<span class='success'>✓</span> 找到测试网站数据 (ID: " . $test_website['web_id'] . ")<br>";
            $test_web_id = $test_website['web_id'];
            
            // 测试获取网站详情的关键函数
            if (function_exists('get_one_website')) {
                $where = "w.web_status=3 AND w.web_id=$test_web_id";
                $web = get_one_website($where);
                if ($web) {
                    echo "<span class='success'>✓</span> get_one_website 函数正常<br>";
                } else {
                    echo "<span class='error'>✗</span> get_one_website 返回空结果<br>";
                }
            } else {
                echo "<span class='error'>✗</span> get_one_website 函数不存在<br>";
            }
            
            // 测试评论相关函数
            if (function_exists('get_website_comments')) {
                $comments = get_website_comments($test_web_id, 5);
                echo "<span class='success'>✓</span> get_website_comments 函数正常，返回 " . count($comments) . " 条评论<br>";
            }
            
            if (function_exists('get_website_comment_stats')) {
                $stats = get_website_comment_stats($test_web_id);
                echo "<span class='success'>✓</span> get_website_comment_stats 函数正常<br>";
            }
            
        } else {
            echo "<span class='warning'>⚠</span> 没有找到已审核的网站数据<br>";
        }
        
    } catch (Exception $e) {
        echo "<span class='error'>✗</span> 网站详情页逻辑测试失败: " . $e->getMessage() . "<br>";
    }
} else {
    echo "<span class='error'>✗</span> 无法测试，数据库对象不存在<br>";
}

// 6. 检查模板文件
echo "<h2>6. 模板文件检查</h2>";
if (file_exists('themes/default/siteinfo.html')) {
    echo "<span class='success'>✓</span> siteinfo.html 模板存在<br>";
    
    // 检查模板文件大小
    $template_size = filesize('themes/default/siteinfo.html');
    echo "<span class='info'>ℹ</span> 模板文件大小: " . number_format($template_size) . " 字节<br>";
    
    // 检查模板是否包含关键内容
    $template_content = file_get_contents('themes/default/siteinfo.html');
    if (strpos($template_content, 'comments-list') !== false) {
        echo "<span class='success'>✓</span> 模板包含评论列表容器<br>";
    } else {
        echo "<span class='warning'>⚠</span> 模板可能缺少评论列表容器<br>";
    }
    
    if (strpos($template_content, 'loadComments') !== false) {
        echo "<span class='success'>✓</span> 模板包含评论加载函数<br>";
    } else {
        echo "<span class='warning'>⚠</span> 模板可能缺少评论加载函数<br>";
    }
    
} else {
    echo "<span class='error'>✗</span> siteinfo.html 模板不存在<br>";
}

// 7. 检查PHP错误日志
echo "<h2>7. PHP错误检查</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<span class='info'>ℹ</span> PHP错误日志位置: $error_log<br>";
    
    // 读取最近的错误
    $log_content = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $log_content), -10);
    
    echo "<h3>最近的错误日志:</h3>";
    echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;'>";
    foreach ($recent_errors as $error) {
        if (trim($error)) {
            echo htmlspecialchars($error) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<span class='warning'>⚠</span> 无法找到PHP错误日志<br>";
}

echo "<h2>8. 建议的解决方案</h2>";
echo "<ul>";
echo "<li>如果上述检查发现错误，请先解决这些基础问题</li>";
echo "<li>检查服务器错误日志中是否有相关错误信息</li>";
echo "<li>尝试访问: <a href='?mod=siteinfo&wid=$test_web_id'>测试网站详情页</a></li>";
echo "<li>如果仍然白屏，请检查浏览器开发者工具的控制台和网络选项卡</li>";
echo "</ul>";

?>
