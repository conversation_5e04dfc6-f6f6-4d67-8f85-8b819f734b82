<?php
/**
 * Sitemap 修复脚本
 * 解决Google Search Console显示网页总数为0的问题
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

// 加载必要文件
require_once(APP_PATH.'init.php');
require_once(APP_PATH.'module/website.php');
require_once(APP_PATH.'module/article.php');
require_once(APP_PATH.'module/category.php');

/**
 * 生成修复版的sitemap索引文件
 */
function generate_fixed_sitemap_index() {
    global $DB, $options;
    
    // 检查各类数据是否存在
    $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
    $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
    $category_count = $DB->get_count($DB->table('categories'), "1=1");
    
    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // 只包含有数据的sitemap
    if ($website_count > 0) {
        echo "    <sitemap>\n";
        echo "        <loc>" . htmlspecialchars($options['site_url'] . "?mod=sitemap&type=webdir&format=xml") . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "    </sitemap>\n";
    }
    
    if ($article_count > 0) {
        echo "    <sitemap>\n";
        echo "        <loc>" . htmlspecialchars($options['site_url'] . "?mod=sitemap&type=article&format=xml") . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "    </sitemap>\n";
    }
    
    if ($category_count > 0) {
        echo "    <sitemap>\n";
        echo "        <loc>" . htmlspecialchars($options['site_url'] . "?mod=sitemap&type=category&format=xml") . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "    </sitemap>\n";
    }
    
    // 总是包含主sitemap（包含首页等基本页面）
    echo "    <sitemap>\n";
    echo "        <loc>" . htmlspecialchars($options['site_url'] . "?mod=sitemap&type=all&format=xml") . "</loc>\n";
    echo "        <lastmod>" . date('c') . "</lastmod>\n";
    echo "    </sitemap>\n";
    
    echo "</sitemapindex>\n";
}

/**
 * 生成增强版的all sitemap，确保包含基本页面
 */
function generate_enhanced_all_sitemap() {
    global $DB, $options;
    
    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // 首页 - 最高优先级
    echo "    <url>\n";
    echo "        <loc>" . htmlspecialchars($options['site_url']) . "</loc>\n";
    echo "        <lastmod>" . date('c') . "</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n";
    
    // 基本页面
    $basic_pages = [
        '?mod=category' => ['分类页面', 'weekly', '0.8'],
        '?mod=search' => ['搜索页面', 'weekly', '0.7'],
        '?mod=submit' => ['网站提交页面', 'monthly', '0.6'],
        '?mod=about' => ['关于我们', 'monthly', '0.5'],
        '?mod=contact' => ['联系我们', 'monthly', '0.5']
    ];
    
    foreach ($basic_pages as $url => $info) {
        echo "    <url>\n";
        echo "        <loc>" . htmlspecialchars($options['site_url'] . $url) . "</loc>\n";
        echo "        <lastmod>" . date('c') . "</lastmod>\n";
        echo "        <changefreq>{$info[1]}</changefreq>\n";
        echo "        <priority>{$info[2]}</priority>\n";
        echo "    </url>\n";
    }
    
    // 添加一些网站数据（限制数量避免过大）
    try {
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM " . $DB->table('websites') . 
               " WHERE web_status=3 ORDER BY web_id DESC LIMIT 50";
        $query = $DB->query($sql);
        
        while ($row = $DB->fetch_array($query)) {
            $web_url = get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']);
            echo "    <url>\n";
            echo "        <loc>" . htmlspecialchars($web_url) . "</loc>\n";
            echo "        <lastmod>" . date('c', $row['web_ctime']) . "</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            echo "        <priority>0.6</priority>\n";
            echo "    </url>\n";
        }
        $DB->free_result($query);
    } catch (Exception $e) {
        // 如果查询失败，继续执行
    }
    
    // 添加一些文章数据
    try {
        $sql = "SELECT art_id, art_ctime FROM " . $DB->table('articles') . 
               " WHERE art_status=3 ORDER BY art_id DESC LIMIT 30";
        $query = $DB->query($sql);
        
        while ($row = $DB->fetch_array($query)) {
            $art_url = get_article_url($row['art_id'], true);
            echo "    <url>\n";
            echo "        <loc>" . htmlspecialchars($art_url) . "</loc>\n";
            echo "        <lastmod>" . date('c', $row['art_ctime']) . "</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            echo "        <priority>0.6</priority>\n";
            echo "    </url>\n";
        }
        $DB->free_result($query);
    } catch (Exception $e) {
        // 如果查询失败，继续执行
    }
    
    echo "</urlset>\n";
}

// 根据请求类型生成相应的sitemap
$action = isset($_GET['action']) ? $_GET['action'] : 'index';

switch ($action) {
    case 'index':
        generate_fixed_sitemap_index();
        break;
    case 'all':
        generate_enhanced_all_sitemap();
        break;
    case 'test':
        // 测试模式，显示HTML格式的诊断信息
        echo "<!DOCTYPE html>\n";
        echo "<html><head><meta charset='utf-8'><title>Sitemap 修复测试</title></head><body>\n";
        echo "<h1>Sitemap 修复测试</h1>\n";
        
        $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
        $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
        $category_count = $DB->get_count($DB->table('categories'), "1=1");
        
        echo "<p><strong>网站数量:</strong> {$website_count}</p>\n";
        echo "<p><strong>文章数量:</strong> {$article_count}</p>\n";
        echo "<p><strong>分类数量:</strong> {$category_count}</p>\n";
        
        echo "<h2>测试链接:</h2>\n";
        echo "<ul>\n";
        echo "<li><a href='sitemap_fix.php?action=index'>修复版 Sitemap 索引</a></li>\n";
        echo "<li><a href='sitemap_fix.php?action=all'>增强版 All Sitemap</a></li>\n";
        echo "<li><a href='?mod=sitemap&type=webdir&format=xml'>原版 Webdir Sitemap</a></li>\n";
        echo "<li><a href='?mod=sitemap&type=article&format=xml'>原版 Article Sitemap</a></li>\n";
        echo "</ul>\n";
        
        echo "</body></html>\n";
        break;
    default:
        generate_fixed_sitemap_index();
        break;
}
?>
