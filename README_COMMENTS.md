# 网站点评功能说明

## 功能概述

为95分类目录网站详情页添加了完整的网站点评功能，支持匿名和会员评论，包含星级评分系统和回复功能。

## 主要特性

### ✅ 评论功能
- **匿名评论**：显示格式为 `匿名 ( 116.147.250.* )`
- **会员评论**：显示会员昵称和邮箱
- **内容过滤**：自动屏蔽所有域名后缀（.com、.cn、.net等）
- **关键词过滤**：集成后台非法关键词过滤系统，自动拦截违规内容
- **防刷机制**：同IP 5分钟内限制重复提交

### ⭐ 星级评分
- **内容质量**：1-5星评分
- **网站服务**：1-5星评分  
- **网站诚信**：1-5星评分
- **综合评分**：自动计算平均分

### 💬 回复功能
- 支持对评论进行回复
- 回复内容嵌套显示
- 匿名和会员都可以回复

### 📊 统计功能
- 总评论数统计
- 各维度平均评分
- 星级可视化显示

## 安装步骤

### 1. 创建数据库表
```bash
# 方法一：运行安装脚本（推荐）
访问：http://你的域名/install_comments.php

# 方法二：手动执行SQL
在数据库中执行：data/sql/add_website_comments_table.sql
```

### 2. 测试功能
```bash
# 运行测试脚本
访问：http://你的域名/test_comments.php
```

### 3. 使用功能
访问任意网站详情页，在"本类排行榜"下方即可看到评论功能。

## 文件结构

```
├── data/sql/
│   └── add_website_comments_table.sql     # 数据库表创建脚本
├── module/
│   ├── website_comments.php               # 评论功能核心模块
│   └── comment_handler.php                # AJAX请求处理器
├── themes/default/
│   └── siteinfo.html                      # 网站详情页模板（已修改）
├── install_comments.php                   # 安装脚本
├── test_comments.php                      # 测试脚本
└── README_COMMENTS.md                     # 本说明文档
```

## 数据库表结构

### dir_website_comments 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| comment_id | int(10) | 评论ID（主键）|
| web_id | int(10) | 网站ID |
| user_id | int(10) | 用户ID（0=匿名）|
| user_email | varchar(50) | 用户邮箱 |
| user_name | varchar(50) | 用户昵称 |
| user_ip | varchar(45) | 用户IP地址 |
| content_quality | tinyint(1) | 内容质量评分(1-5) |
| service_quality | tinyint(1) | 服务质量评分(1-5) |
| trust_level | tinyint(1) | 诚信度评分(1-5) |
| comment_content | text | 评论内容 |
| parent_id | int(10) | 父评论ID（0=主评论）|
| status | tinyint(1) | 状态（1=正常，0=隐藏）|
| create_time | int(10) | 创建时间戳 |

## API接口

### 提交评论
```javascript
POST module/comment_handler.php
参数：
- action: submit_comment
- web_id: 网站ID
- content_quality: 内容质量评分(1-5)
- service_quality: 服务质量评分(1-5)  
- trust_level: 诚信度评分(1-5)
- comment_content: 评论内容
```

### 获取评论列表
```javascript
POST module/comment_handler.php
参数：
- action: get_comments
- web_id: 网站ID
- limit: 限制数量（可选，默认10）
```

### 提交回复
```javascript
POST module/comment_handler.php
参数：
- action: submit_reply
- web_id: 网站ID
- parent_id: 父评论ID
- comment_content: 回复内容
```

## 域名过滤规则

系统会自动过滤以下域名后缀：
- 通用顶级域名：com, net, org, info, biz 等
- 国家代码域名：cn, uk, jp, de 等  
- 新通用域名：app, dev, tech, online 等
- 中文域名：中国, 网络, 公司 等

过滤后的域名会被替换为 `***`

## 安全特性

1. **SQL注入防护**：所有数据库操作使用参数化查询
2. **XSS防护**：用户输入内容进行HTML转义
3. **CSRF防护**：验证请求来源
4. **频率限制**：同IP 5分钟内限制重复提交
5. **内容过滤**：自动过滤域名和敏感内容
6. **关键词过滤**：集成后台非法关键词过滤系统
7. **IP掩码**：保护用户隐私，只显示部分IP

## 自定义配置

### 修改评分维度
在 `themes/default/siteinfo.html` 中修改评分项目：
```html
<div class="rating-item">
    <label>自定义评分项：</label>
    <div class="star-rating" data-rating="custom_rating">
        <!-- 星级评分HTML -->
    </div>
</div>
```

### 修改过滤规则
在 `module/website_comments.php` 的 `filter_domain_names()` 函数中修改域名后缀列表。

### 配置关键词过滤
1. 登录后台管理系统
2. 进入 系统设置 → 基本设置
3. 找到 '非法关键词过滤设置' 选项
4. 在文本框中输入关键词，用英文逗号分隔
5. 例如：色情,赌博,毒品,诈骗,垃圾,广告
6. 保存设置

### 修改样式
在 `themes/default/siteinfo.html` 的 `<style>` 标签中修改CSS样式。

## 常见问题

### Q: 评论提交后没有显示？
A: 检查数据库表是否创建成功，运行测试脚本确认功能正常。

### Q: 星级评分不能点击？
A: 检查JavaScript是否正常加载，确认没有JS错误。

### Q: 域名过滤不生效？
A: 检查 `filter_domain_names()` 函数是否正常工作，可在测试脚本中验证。

### Q: 会员评论显示异常？
A: 检查用户登录状态，确认 `check_user_login()` 函数正常工作。

## 技术支持

如有问题，请检查：
1. 数据库表是否正确创建
2. 文件权限是否正确
3. PHP错误日志
4. 浏览器控制台错误信息

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持匿名和会员评论
- 三维度星级评分系统
- 评论回复功能
- 域名自动过滤
- IP地址掩码保护
- 评论统计功能
