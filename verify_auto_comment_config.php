<?php
/**
 * 验证自动评论配置
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>验证自动评论配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        .btn-large { font-size: 16px; padding: 15px 30px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>✅ 验证自动评论配置</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    $options_table = $DB->table('options');
    
    // 1. 验证配置选项
    echo "<h2>1. 验证配置选项</h2>";
    
    $config_options = array(
        'auto_comment_enabled' => 'yes',
        'auto_comment_count' => '5',
        'auto_comment_delay' => 'yes',
        'auto_comment_min_delay' => '1',
        'auto_comment_max_delay' => '30'
    );
    
    echo "<table>";
    echo "<tr><th>配置项</th><th>数据库值</th><th>状态</th></tr>";
    
    $all_ok = true;
    foreach ($config_options as $option_name => $expected_value) {
        $current = $DB->fetch_one("SELECT option_value FROM $options_table WHERE option_name = '$option_name'");
        $current_value = $current ? $current['option_value'] : '不存在';
        $status = ($current_value !== '不存在') ? '✅ 正常' : '❌ 缺失';
        
        if ($current_value === '不存在') {
            $all_ok = false;
        }
        
        echo "<tr>";
        echo "<td>{$option_name}</td>";
        echo "<td>{$current_value}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 测试配置加载
    echo "<h2>2. 测试配置加载</h2>";
    
    if (file_exists(ROOT_PATH.'module/auto_comments.php')) {
        require_once(ROOT_PATH.'module/auto_comments.php');
        
        if (function_exists('get_auto_comment_config')) {
            $config = get_auto_comment_config();
            echo "<div class='success'>✅ 配置函数正常工作</div>";
            echo "<div class='info'>";
            echo "<strong>当前配置状态：</strong><br>";
            echo "功能状态: " . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "评论数量: {$config['comment_count']} 条<br>";
            echo "延迟发布: " . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "延迟范围: {$config['min_delay']}-{$config['max_delay']} 分钟<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>❌ 配置函数不存在</div>";
            $all_ok = false;
        }
    } else {
        echo "<div class='error'>❌ 自动评论模块文件不存在</div>";
        $all_ok = false;
    }
    
    // 3. 检查评论模板表
    echo "<h2>3. 检查评论模板表</h2>";
    
    $template_table = $DB->table('auto_comment_templates');
    $table_exists = $DB->query("SHOW TABLES LIKE '$template_table'");
    
    if ($DB->num_rows($table_exists)) {
        $template_count = $DB->get_count($template_table, '1');
        $active_count = $DB->get_count($template_table, 'status = 1');
        
        echo "<div class='success'>✅ 评论模板表存在</div>";
        echo "<div class='info'>总模板数: {$template_count} 条，启用: {$active_count} 条</div>";
    } else {
        echo "<div class='error'>❌ 评论模板表不存在</div>";
        $all_ok = false;
    }
    
    // 4. 检查后台文件
    echo "<h2>4. 检查后台文件</h2>";
    
    $backend_files = array(
        'system/option.php' => '后台配置控制器',
        'themes/system/option.html' => '配置页面模板',
        'system/auto_comment_templates.php' => '模板管理控制器',
        'themes/system/auto_comment_templates.html' => '模板管理页面'
    );
    
    foreach ($backend_files as $file => $description) {
        if (file_exists(ROOT_PATH . $file)) {
            echo "<div class='success'>✅ {$description}: {$file}</div>";
        } else {
            echo "<div class='error'>❌ {$description}: {$file} 不存在</div>";
            $all_ok = false;
        }
    }
    
    // 5. 最终结果
    echo "<h2>5. 验证结果</h2>";
    
    if ($all_ok) {
        echo "<div class='success'>";
        echo "<h3>🎉 配置验证成功！</h3>";
        echo "<p>所有自动评论功能组件都已正确安装和配置。</p>";
        echo "<ul>";
        echo "<li>✅ 数据库配置选项正常</li>";
        echo "<li>✅ 配置加载函数正常</li>";
        echo "<li>✅ 评论模板表存在</li>";
        echo "<li>✅ 后台管理文件完整</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h4>📋 下一步操作</h4>";
        echo "<ol>";
        echo "<li><strong>访问后台配置页面</strong> - 点击下方按钮进入配置</li>";
        echo "<li><strong>启用自动评论功能</strong> - 在配置页面选择'开启'</li>";
        echo "<li><strong>调整参数设置</strong> - 根据需要调整评论数量和延迟时间</li>";
        echo "<li><strong>保存配置</strong> - 点击'保存'按钮使配置生效</li>";
        echo "<li><strong>测试功能</strong> - 审核一个网站测试自动评论</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px; text-align: center;'>";
        echo "<a href='system/option.php?opt=comment' class='btn btn-large' target='_blank'>🚀 前往后台配置页面</a>";
        echo "<br><br>";
        echo "<a href='system/auto_comment_templates.php' class='btn' target='_blank'>📝 管理评论模板</a>";
        echo "<a href='test_auto_comments.php' class='btn' target='_blank'>🧪 测试功能</a>";
        echo "<a href='demo_auto_comments.php' class='btn' target='_blank'>🎬 功能演示</a>";
        echo "</div>";
        
        echo "<div class='info' style='margin-top: 30px;'>";
        echo "<h4>💡 重要提示</h4>";
        echo "<ul>";
        echo "<li>配置页面会在新窗口打开，请确保浏览器允许弹窗</li>";
        echo "<li>如果配置页面显示空白，请清理浏览器缓存后重试</li>";
        echo "<li>功能启用后，每次网站审核通过都会自动添加评论</li>";
        echo "<li>可以随时在后台调整配置参数</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 配置验证失败</h3>";
        echo "<p>部分组件缺失或配置不正确，请重新安装。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='install_auto_comments.php' class='btn'>🔧 重新安装</a>";
        echo "<a href='fix_auto_comment_config.php' class='btn'>🛠️ 修复配置</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 验证失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
