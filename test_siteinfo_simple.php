<?php
/**
 * 简化的网站详情页测试
 * 用于排查白屏问题
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "开始测试网站详情页...<br>";

try {
    // 1. 测试基础包含
    echo "1. 包含初始化文件...<br>";
    if (!file_exists('source/init.php')) {
        die("错误: source/init.php 文件不存在");
    }
    
    require_once('source/init.php');
    echo "✓ 初始化文件加载成功<br>";
    
    // 2. 检查关键变量
    echo "2. 检查关键变量...<br>";
    if (!isset($DB)) {
        die("错误: 数据库对象不存在");
    }
    echo "✓ 数据库对象存在<br>";
    
    if (!isset($smarty)) {
        die("错误: Smarty对象不存在");
    }
    echo "✓ Smarty对象存在<br>";
    
    // 3. 测试评论模块包含
    echo "3. 包含评论模块...<br>";
    if (!file_exists('module/website_comments.php')) {
        die("错误: module/website_comments.php 文件不存在");
    }
    
    require_once('module/website_comments.php');
    echo "✓ 评论模块加载成功<br>";
    
    // 4. 模拟网站详情页逻辑
    echo "4. 模拟网站详情页逻辑...<br>";
    
    // 获取一个测试网站ID
    $test_web_id = isset($_GET['wid']) ? intval($_GET['wid']) : 1;
    
    // 检查网站是否存在
    $where = "w.web_status=3 AND w.web_id=$test_web_id";
    
    if (!function_exists('get_one_website')) {
        die("错误: get_one_website 函数不存在");
    }
    
    $web = get_one_website($where);
    if (!$web) {
        echo "警告: 网站ID $test_web_id 不存在或未审核，尝试查找其他网站...<br>";
        
        // 查找任意一个已审核的网站
        $websites_table = $DB->table('websites');
        $webdata_table = $DB->table('webdata');
        
        $test_website = $DB->fetch_one("SELECT w.web_id FROM $websites_table w WHERE w.web_status = 3 LIMIT 1");
        if ($test_website) {
            $test_web_id = $test_website['web_id'];
            $where = "w.web_status=3 AND w.web_id=$test_web_id";
            $web = get_one_website($where);
            echo "✓ 找到测试网站 ID: $test_web_id<br>";
        } else {
            die("错误: 没有找到任何已审核的网站");
        }
    }
    
    if (!$web) {
        die("错误: 无法获取网站信息");
    }
    
    echo "✓ 网站信息获取成功: " . $web['web_name'] . "<br>";
    
    // 5. 测试评论功能
    echo "5. 测试评论功能...<br>";
    
    try {
        $comments = get_website_comments($test_web_id, 5);
        echo "✓ 评论获取成功，共 " . count($comments) . " 条<br>";
        
        $stats = get_website_comment_stats($test_web_id);
        echo "✓ 评论统计获取成功<br>";
        
    } catch (Exception $e) {
        echo "警告: 评论功能测试失败 - " . $e->getMessage() . "<br>";
    }
    
    // 6. 测试模板渲染
    echo "6. 测试模板渲染...<br>";
    
    if (!file_exists('themes/default/siteinfo.html')) {
        die("错误: themes/default/siteinfo.html 模板文件不存在");
    }
    
    // 设置基本变量
    $pagename = '站点详细';
    $pageurl = '?mod=siteinfo';
    $tempfile = 'siteinfo.html';
    
    // 获取分类信息
    if (function_exists('get_one_category')) {
        $cate = get_one_category($web['cate_id']);
    } else {
        $cate = array('cate_name' => '未分类', 'cate_id' => 0);
    }
    
    // 获取用户信息
    if (function_exists('get_one_user')) {
        $user = get_one_user($web['user_id']);
    } else {
        $user = array('nick_name' => '未知用户');
    }
    
    // 设置SEO信息
    $seo_title = $web['web_name'] . ' - 网站详情 - ' . $options['site_name'];
    $smarty->assign('site_title', $seo_title);
    $smarty->assign('site_keywords', $web['web_name'] . ',' . $options['site_keywords']);
    $smarty->assign('site_description', $web['web_intro'] ? $web['web_intro'] : $web['web_name']);
    
    // 处理网站数据
    $web['web_furl'] = format_url($web['web_url']);
    $web['web_pic'] = get_webthumb($web['web_pic']);
    $web['web_ip'] = isset($web['web_ip']) ? long2ip($web['web_ip']) : '';
    $web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
    $web['web_utime'] = isset($web['web_utime']) ? date('Y-m-d', $web['web_utime']) : date('Y-m-d');
    
    // 分配变量到模板
    $smarty->assign('web', $web);
    $smarty->assign('user', $user);
    $smarty->assign('cate_id', $cate['cate_id']);
    $smarty->assign('cate_name', $cate['cate_name']);
    
    echo "✓ 模板变量设置完成<br>";
    
    // 7. 尝试渲染模板
    echo "7. 尝试渲染模板...<br>";
    
    // 检查模板是否可以编译
    try {
        $smarty->testInstall();
        echo "✓ Smarty安装测试通过<br>";
    } catch (Exception $e) {
        echo "警告: Smarty测试失败 - " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
    echo "<h2>测试完成 - 尝试显示简化版网站详情页</h2>";
    echo "<p><strong>网站名称:</strong> " . htmlspecialchars($web['web_name']) . "</p>";
    echo "<p><strong>网站地址:</strong> " . htmlspecialchars($web['web_url']) . "</p>";
    echo "<p><strong>网站介绍:</strong> " . htmlspecialchars($web['web_intro']) . "</p>";
    echo "<p><strong>分类:</strong> " . htmlspecialchars($cate['cate_name']) . "</p>";
    
    echo "<hr>";
    echo "<p><a href='?mod=siteinfo&wid=$test_web_id'>尝试访问完整的网站详情页</a></p>";
    echo "<p><a href='diagnose_siteinfo.php'>运行完整诊断</a></p>";
    
} catch (ParseError $e) {
    echo "<h2 style='color:red;'>PHP解析错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<h2 style='color:red;'>PHP致命错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
} catch (Exception $e) {
    echo "<h2 style='color:red;'>异常错误</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
}

?>
