<?php
require('common.php');

// 加载网站配置
if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
require(ROOT_PATH.'data/static/options.php');
$options = $static_data;

// 加载搜索推送模块
require(APP_PATH.'module/search_push.php');

$fileurl = 'search_push_simple.php';
$tempfile = 'search_push_simple.html';
$pagetitle = '搜索引擎推送配置';

// 获取当前配置
$config = get_push_config();

// 处理配置保存
if ($_POST['submit']) {
    $baidu_token = trim($_POST['baidu_token']);
    $google_key = trim($_POST['google_key']);
    $bing_key = trim($_POST['bing_key']);
    
    if (save_push_config($baidu_token, $google_key, $bing_key)) {
        msgbox('配置保存成功！', $fileurl);
    } else {
        msgbox('配置保存失败！');
    }
}

$smarty->assign('config', $config);

smarty_output($tempfile);
?>
