<?php
/**
 * 设置自动评论配置选项
 * 确保后台配置页面能正常显示
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>设置自动评论配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>⚙️ 设置自动评论配置</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    $options_table = $DB->table('options');
    
    // 定义配置选项
    $config_options = array(
        'auto_comment_enabled' => 'yes',
        'auto_comment_count' => '5',
        'auto_comment_delay' => 'yes',
        'auto_comment_min_delay' => '1',
        'auto_comment_max_delay' => '30'
    );
    
    echo "<h2>1. 检查并设置配置选项</h2>";
    
    $updated_count = 0;
    $inserted_count = 0;
    
    foreach ($config_options as $option_name => $default_value) {
        // 检查选项是否存在
        $existing = $DB->fetch_one("SELECT option_value FROM $options_table WHERE option_name = '$option_name'");
        
        if ($existing) {
            // 如果存在但值为空，则更新
            if (empty($existing['option_value'])) {
                $DB->update($options_table, 
                    array('option_value' => $default_value), 
                    array('option_name' => $option_name)
                );
                echo "<div class='info'>更新配置: {$option_name} = {$default_value}</div>";
                $updated_count++;
            } else {
                echo "<div class='success'>✅ 配置已存在: {$option_name} = {$existing['option_value']}</div>";
            }
        } else {
            // 插入新配置
            $DB->insert($options_table, array(
                'option_name' => $option_name,
                'option_value' => $default_value
            ));
            echo "<div class='success'>✅ 插入配置: {$option_name} = {$default_value}</div>";
            $inserted_count++;
        }
    }
    
    echo "<div class='info'>操作完成：插入 {$inserted_count} 个，更新 {$updated_count} 个配置选项</div>";
    
    // 2. 更新配置缓存
    echo "<h2>2. 更新配置缓存</h2>";
    
    if (function_exists('update_cache')) {
        update_cache('options');
        echo "<div class='success'>✅ 配置缓存已更新</div>";
    } else {
        echo "<div class='info'>update_cache 函数不存在，手动清理缓存</div>";
        
        // 手动清理缓存
        $cache_dirs = array(
            ROOT_PATH . 'data/cache/',
            ROOT_PATH . 'data/compile/'
        );
        
        $deleted_files = 0;
        foreach ($cache_dirs as $cache_dir) {
            if (is_dir($cache_dir)) {
                $files = glob($cache_dir . '*');
                foreach ($files as $file) {
                    if (is_file($file) && unlink($file)) {
                        $deleted_files++;
                    }
                }
            }
        }
        echo "<div class='success'>✅ 清理了 {$deleted_files} 个缓存文件</div>";
    }
    
    // 3. 验证配置
    echo "<h2>3. 验证配置</h2>";
    
    // 重新加载配置
    unset($options);
    $query = $DB->query("SELECT option_name, option_value FROM $options_table");
    $options = array();
    while ($row = $DB->fetch_array($query)) {
        $options[$row['option_name']] = $row['option_value'];
    }
    
    echo "<table>";
    echo "<tr><th>配置项</th><th>当前值</th><th>状态</th></tr>";
    
    $all_ok = true;
    foreach ($config_options as $option_name => $default_value) {
        $current_value = isset($options[$option_name]) ? $options[$option_name] : '未设置';
        $status = ($current_value !== '未设置') ? '✅ 正常' : '❌ 缺失';
        
        if ($current_value === '未设置') {
            $all_ok = false;
        }
        
        echo "<tr>";
        echo "<td>{$option_name}</td>";
        echo "<td>{$current_value}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. 测试后台页面访问
    echo "<h2>4. 测试后台页面</h2>";
    
    if ($all_ok) {
        echo "<div class='success'>";
        echo "<h3>🎉 配置设置完成！</h3>";
        echo "<p>所有自动评论配置选项已正确设置，现在可以正常访问后台配置页面了。</p>";
        echo "<ul>";
        echo "<li>✅ 配置选项已添加到数据库</li>";
        echo "<li>✅ 配置缓存已更新</li>";
        echo "<li>✅ 后台导航菜单已配置</li>";
        echo "<li>✅ 配置页面模板已就绪</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h4>📋 使用说明</h4>";
        echo "<ol>";
        echo "<li>点击下方按钮访问后台配置页面</li>";
        echo "<li>在配置页面中启用自动评论功能</li>";
        echo "<li>设置合适的评论数量和延迟时间</li>";
        echo "<li>保存配置后功能即可生效</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px; text-align: center;'>";
        echo "<a href='system/option.php?opt=comment' class='btn' style='font-size: 16px; padding: 15px 30px;'>🚀 前往后台配置页面</a>";
        echo "<br><br>";
        echo "<a href='system/auto_comment_templates.php' class='btn'>管理评论模板</a>";
        echo "<a href='test_auto_comments.php' class='btn'>测试功能</a>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 配置设置不完整</h3>";
        echo "<p>部分配置选项仍然缺失，请重新运行安装脚本。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='install_auto_comments.php' class='btn'>重新安装</a>";
        echo "<a href='?retry=1' class='btn'>重试设置</a>";
        echo "</div>";
    }
    
    // 处理重试请求
    if (isset($_GET['retry']) && $_GET['retry'] == '1') {
        echo "<script>setTimeout(function(){ location.href = location.pathname; }, 2000);</script>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 设置失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
