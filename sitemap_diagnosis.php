<?php
/**
 * Sitemap 问题诊断脚本
 * 用于检测和解决Google Search Console显示网页总数为0的问题
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

// 错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<meta charset='utf-8'>\n";
echo "<title>Sitemap 诊断报告</title>\n";
echo "<style>\n";
echo "body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .warning{color:orange;}\n";
echo ".section{margin:20px 0;padding:15px;border:1px solid #ddd;border-radius:5px;}\n";
echo "pre{background:#f5f5f5;padding:10px;border:1px solid #ccc;max-height:300px;overflow-y:auto;}\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>Sitemap 诊断报告</h1>\n";
echo "<p>诊断时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 1. 检查基础文件
echo "<div class='section'>\n";
echo "<h2>1. 基础文件检查</h2>\n";
$required_files = [
    'source/init.php',
    'source/module/website.php', 
    'source/module/article.php',
    'source/module/category.php',
    'module/sitemap.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✓ {$file} 存在</span><br>\n";
    } else {
        echo "<span class='error'>✗ {$file} 不存在</span><br>\n";
    }
}
echo "</div>\n";

// 2. 尝试加载核心文件
echo "<div class='section'>\n";
echo "<h2>2. 核心文件加载测试</h2>\n";
try {
    require_once(APP_PATH.'init.php');
    echo "<span class='success'>✓ init.php 加载成功</span><br>\n";
} catch (Exception $e) {
    echo "<span class='error'>✗ init.php 加载失败: " . $e->getMessage() . "</span><br>\n";
    echo "</div></body></html>";
    exit;
}

try {
    require_once(APP_PATH.'module/website.php');
    echo "<span class='success'>✓ website.php 加载成功</span><br>\n";
} catch (Exception $e) {
    echo "<span class='error'>✗ website.php 加载失败: " . $e->getMessage() . "</span><br>\n";
}

try {
    require_once(APP_PATH.'module/article.php');
    echo "<span class='success'>✓ article.php 加载成功</span><br>\n";
} catch (Exception $e) {
    echo "<span class='error'>✗ article.php 加载失败: " . $e->getMessage() . "</span><br>\n";
}

try {
    require_once(APP_PATH.'module/category.php');
    echo "<span class='success'>✓ category.php 加载成功</span><br>\n";
} catch (Exception $e) {
    echo "<span class='error'>✗ category.php 加载失败: " . $e->getMessage() . "</span><br>\n";
}
echo "</div>\n";

// 3. 检查sitemap函数
echo "<div class='section'>\n";
echo "<h2>3. Sitemap 函数检查</h2>\n";
$sitemap_functions = [
    'get_all_sitemap',
    'get_website_sitemap', 
    'get_article_sitemap',
    'get_article_detail_sitemap',
    'get_category_sitemap',
    'get_vip_sitemap',
    'get_tags_sitemap',
    'get_pending_sitemap',
    'get_rejected_sitemap',
    'get_blacklist_sitemap',
    'get_search_sitemap',
    'get_member_sitemap',
    'get_other_sitemap'
];

foreach ($sitemap_functions as $func) {
    if (function_exists($func)) {
        echo "<span class='success'>✓ {$func} 函数存在</span><br>\n";
    } else {
        echo "<span class='error'>✗ {$func} 函数不存在</span><br>\n";
    }
}
echo "</div>\n";

// 4. 检查数据库连接和数据
echo "<div class='section'>\n";
echo "<h2>4. 数据库检查</h2>\n";
if (isset($DB) && is_object($DB)) {
    echo "<span class='success'>✓ 数据库连接正常</span><br>\n";
    
    // 检查网站数据
    try {
        $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
        echo "<span class='success'>✓ 已审核网站数量: {$website_count}</span><br>\n";
        
        if ($website_count == 0) {
            echo "<span class='warning'>⚠ 警告: 没有已审核的网站数据，这可能是sitemap为空的原因</span><br>\n";
        }
    } catch (Exception $e) {
        echo "<span class='error'>✗ 网站数据查询失败: " . $e->getMessage() . "</span><br>\n";
    }
    
    // 检查文章数据
    try {
        $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
        echo "<span class='success'>✓ 已发布文章数量: {$article_count}</span><br>\n";
        
        if ($article_count == 0) {
            echo "<span class='warning'>⚠ 警告: 没有已发布的文章数据</span><br>\n";
        }
    } catch (Exception $e) {
        echo "<span class='error'>✗ 文章数据查询失败: " . $e->getMessage() . "</span><br>\n";
    }
} else {
    echo "<span class='error'>✗ 数据库连接失败</span><br>\n";
}
echo "</div>\n";

// 5. 测试sitemap生成
echo "<div class='section'>\n";
echo "<h2>5. Sitemap 生成测试</h2>\n";

// 测试webdir sitemap
echo "<h3>5.1 Webdir Sitemap 测试</h3>\n";
try {
    ob_start();
    get_website_sitemap(0);
    $webdir_output = ob_get_clean();
    
    $url_count = substr_count($webdir_output, '<url>');
    $has_xml_header = strpos($webdir_output, '<?xml') === 0;
    $has_urlset = strpos($webdir_output, '<urlset') !== false;
    
    if ($has_xml_header && $has_urlset && $url_count > 0) {
        echo "<span class='success'>✓ Webdir sitemap 生成成功，包含 {$url_count} 个URL</span><br>\n";
    } else {
        echo "<span class='error'>✗ Webdir sitemap 生成失败或为空</span><br>\n";
        echo "<span class='error'>输出长度: " . strlen($webdir_output) . " 字符</span><br>\n";
    }
    
    echo "<h4>输出预览:</h4>\n";
    echo "<pre>" . htmlspecialchars(substr($webdir_output, 0, 500)) . "</pre>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗ Webdir sitemap 测试异常: " . $e->getMessage() . "</span><br>\n";
}

// 测试article sitemap
echo "<h3>5.2 Article Sitemap 测试</h3>\n";
try {
    ob_start();
    get_article_sitemap(0);
    $article_output = ob_get_clean();
    
    $url_count = substr_count($article_output, '<url>');
    $has_xml_header = strpos($article_output, '<?xml') === 0;
    $has_urlset = strpos($article_output, '<urlset') !== false;
    
    if ($has_xml_header && $has_urlset && $url_count > 0) {
        echo "<span class='success'>✓ Article sitemap 生成成功，包含 {$url_count} 个URL</span><br>\n";
    } else {
        echo "<span class='error'>✗ Article sitemap 生成失败或为空</span><br>\n";
        echo "<span class='error'>输出长度: " . strlen($article_output) . " 字符</span><br>\n";
    }
    
    echo "<h4>输出预览:</h4>\n";
    echo "<pre>" . htmlspecialchars(substr($article_output, 0, 500)) . "</pre>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>✗ Article sitemap 测试异常: " . $e->getMessage() . "</span><br>\n";
}

echo "</div>\n";

// 6. 提供解决方案
echo "<div class='section'>\n";
echo "<h2>6. 问题解决建议</h2>\n";
echo "<h3>可能的问题和解决方案:</h3>\n";
echo "<ol>\n";
echo "<li><strong>数据为空:</strong> 如果网站或文章数量为0，需要先添加一些内容</li>\n";
echo "<li><strong>函数缺失:</strong> 如果某些sitemap函数不存在，需要检查相关模块文件</li>\n";
echo "<li><strong>URL访问问题:</strong> 确保sitemap URL可以正常访问</li>\n";
echo "<li><strong>XML格式问题:</strong> 检查生成的XML是否符合sitemap标准</li>\n";
echo "<li><strong>Google抓取问题:</strong> 可能需要重新提交sitemap到Google Search Console</li>\n";
echo "</ol>\n";

echo "<h3>测试链接:</h3>\n";
echo "<ul>\n";
echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=webdir&format=xml' target='_blank'>测试 Webdir Sitemap</a></li>\n";
echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=article&format=xml' target='_blank'>测试 Article Sitemap</a></li>\n";
echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=all&format=xml' target='_blank'>测试 All Sitemap</a></li>\n";
echo "<li><a href='https://www.95dir.com/sitemap.xml' target='_blank'>测试主 Sitemap 索引</a></li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "</body>\n";
echo "</html>\n";
?>
