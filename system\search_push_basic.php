<?php
require('common.php');

$fileurl = 'search_push_basic.php';
$tempfile = 'search_push_basic.html';

if (!isset($action)) $action = 'config';

if ($action == 'config') {
    $pagetitle = '搜索引擎推送配置';
    
    // 处理配置保存
    if ($_POST['submit']) {
        $baidu_token = trim($_POST['baidu_token']);
        $google_key = trim($_POST['google_key']);
        $bing_key = trim($_POST['bing_key']);
        
        // 直接保存到数据库，不调用任何外部函数
        $table = $DB->table('options');
        $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('baidu_push_token', '$baidu_token')");
        $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('google_api_key', '$google_key')");
        $DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('bing_api_key', '$bing_key')");
        
        msgbox('配置保存成功！', $fileurl);
    }
    
    // 直接从数据库获取配置，不调用任何外部函数
    $table = $DB->table('options');
    $baidu_token = $DB->get_one("SELECT option_value FROM $table WHERE option_name='baidu_push_token'");
    $google_key = $DB->get_one("SELECT option_value FROM $table WHERE option_name='google_api_key'");
    $bing_key = $DB->get_one("SELECT option_value FROM $table WHERE option_name='bing_api_key'");
    
    $config = array(
        'baidu_token' => $baidu_token ? $baidu_token['option_value'] : '',
        'google_key' => $google_key ? $google_key['option_value'] : '',
        'bing_key' => $bing_key ? $bing_key['option_value'] : ''
    );
    
    $smarty->assign('config', $config);
}

smarty_output($tempfile);
?>
