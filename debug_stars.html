<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星级评分调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .star-rating {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            user-select: none;
            display: inline-block;
        }

        .star-rating .star {
            display: inline-block;
            transition: color 0.2s;
            margin-right: 3px;
            cursor: pointer;
            padding: 2px;
            line-height: 1;
        }

        .star-rating .star:hover,
        .star-rating .star.active {
            color: #ffc107;
            text-shadow: 0 0 3px rgba(255, 193, 7, 0.5);
        }

        .star-rating .star.active ~ .star {
            color: #ddd;
        }

        .rating-item {
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .debug {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>星级评分调试页面</h1>
    
    <div class="debug" id="debug-log">
        调试信息将显示在这里...
    </div>

    <form id="comment-form">
        <div class="rating-item">
            <label style="display: inline-block; width: 80px; font-weight: bold;">内容质量：</label>
            <div class="star-rating" data-rating="content_quality" style="display: inline-block;">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <input type="hidden" name="content_quality" value="5">
            <span id="content_quality_display" style="margin-left: 10px; color: #666;">当前: 5星</span>
        </div>

        <div class="rating-item">
            <label style="display: inline-block; width: 80px; font-weight: bold;">网站服务：</label>
            <div class="star-rating" data-rating="service_quality" style="display: inline-block;">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <input type="hidden" name="service_quality" value="5">
            <span id="service_quality_display" style="margin-left: 10px; color: #666;">当前: 5星</span>
        </div>

        <div class="rating-item">
            <label style="display: inline-block; width: 80px; font-weight: bold;">网站诚信：</label>
            <div class="star-rating" data-rating="trust_level" style="display: inline-block;">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <input type="hidden" name="trust_level" value="5">
            <span id="trust_level_display" style="margin-left: 10px; color: #666;">当前: 5星</span>
        </div>
    </form>

    <button onclick="showValues()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-top: 20px;">
        显示当前值
    </button>

    <div id="values-display" style="margin-top: 20px;"></div>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<br>[${time}] ${message}`;
            console.log(message);
        }

        // 更新星级显示
        function updateStars(stars, value) {
            stars.forEach((star, index) => {
                if (index < value) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // 初始化星级评分
        function initStarRating() {
            log('开始初始化星级评分...');
            
            const ratingTypes = ['content_quality', 'service_quality', 'trust_level'];
            
            ratingTypes.forEach(ratingName => {
                log(`处理 ${ratingName}`);
                
                const starRating = document.querySelector(`.star-rating[data-rating="${ratingName}"]`);
                const hiddenInput = document.querySelector(`input[name="${ratingName}"]`);
                const display = document.getElementById(`${ratingName}_display`);
                
                if (!starRating) {
                    log(`错误: 找不到星级容器 ${ratingName}`);
                    return;
                }
                
                if (!hiddenInput) {
                    log(`错误: 找不到隐藏输入框 ${ratingName}`);
                    return;
                }
                
                log(`找到组件: ${ratingName}`);
                
                const stars = starRating.querySelectorAll('.star');
                log(`找到 ${stars.length} 个星星`);
                
                // 设置初始显示状态
                const initialValue = parseInt(hiddenInput.value) || 5;
                updateStars(stars, initialValue);
                
                // 为每个星星添加事件
                stars.forEach((star, index) => {
                    const value = index + 1;
                    
                    // 点击事件
                    star.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (display) display.textContent = `当前: ${value}星`;
                        log(`点击设置 ${ratingName} 为 ${value} 星`);
                        return false;
                    };
                    
                    // 鼠标悬停事件
                    star.onmouseover = function() {
                        updateStars(stars, value);
                    };
                    
                    // 触摸事件
                    star.ontouchstart = function(e) {
                        e.preventDefault();
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (display) display.textContent = `当前: ${value}星`;
                        log(`触摸设置 ${ratingName} 为 ${value} 星`);
                        return false;
                    };
                    
                    log(`为第 ${value} 个星星绑定事件`);
                });
                
                // 鼠标离开事件
                starRating.onmouseleave = function() {
                    updateStars(stars, parseInt(hiddenInput.value) || 5);
                };
                
                log(`${ratingName} 初始化完成`);
            });
            
            log('星级评分初始化完成');
        }

        function showValues() {
            const values = {
                content_quality: document.querySelector('input[name="content_quality"]').value,
                service_quality: document.querySelector('input[name="service_quality"]').value,
                trust_level: document.querySelector('input[name="trust_level"]').value
            };
            
            document.getElementById('values-display').innerHTML = `
                <h3>当前评分值：</h3>
                <p><strong>内容质量：</strong> ${values.content_quality} 星</p>
                <p><strong>网站服务：</strong> ${values.service_quality} 星</p>
                <p><strong>网站诚信：</strong> ${values.trust_level} 星</p>
            `;
            
            log(`获取评分值: ${JSON.stringify(values)}`);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成');
            setTimeout(initStarRating, 100); // 延迟100ms确保DOM完全准备好
        });
    </script>
</body>
</html>
