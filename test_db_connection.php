<?php
/**
 * 数据库连接测试脚本
 */

echo "<h1>数据库连接测试</h1>";

try {
    // 引入初始化文件
    require_once('./source/init.php');
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
    echo "✅ 初始化文件加载成功";
    echo "</div>";
    
    // 测试数据库连接
    if (isset($DB)) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
        echo "✅ 数据库对象存在";
        echo "</div>";
        
        // 测试查询
        $test_sql = "SELECT COUNT(*) as count FROM " . $DB->table('websites');
        $result = $DB->fetch_one($test_sql);
        
        if ($result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ 数据库查询成功，网站表有 " . $result['count'] . " 条记录";
            echo "</div>";
        }
        
        // 检查评论表是否存在
        $table_name = $DB->table('website_comments');
        $check_sql = "SHOW TABLES LIKE '$table_name'";
        $check_result = $DB->query($check_sql);
        
        if ($DB->num_rows($check_result) > 0) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ 评论表 $table_name 已存在";
            echo "</div>";
            
            // 查询评论数量
            $comment_sql = "SELECT COUNT(*) as count FROM $table_name";
            $comment_result = $DB->fetch_one($comment_sql);
            echo "<p>当前评论数量：" . $comment_result['count'] . "</p>";
            
        } else {
            echo "<div style='color: orange; padding: 10px; border: 1px solid orange; border-radius: 4px; margin: 10px 0;'>";
            echo "⚠️ 评论表 $table_name 不存在，需要先安装";
            echo "</div>";
            echo "<p><a href='simple_install_comments.php'>点击这里安装评论表</a></p>";
        }
        
        // 显示一些网站信息用于测试
        echo "<h2>可用于测试的网站</h2>";
        $websites_sql = "SELECT web_id, web_name, web_url FROM " . $DB->table('websites') . " WHERE web_status = 3 LIMIT 3";
        $websites_result = $DB->query($websites_sql);
        
        if ($DB->num_rows($websites_result) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>网站ID</th><th>网站名称</th><th>网站地址</th><th>测试链接</th></tr>";
            while ($website = $DB->fetch_array($websites_result)) {
                $test_url = "?mod=siteinfo&wid=" . $website['web_id'];
                echo "<tr>";
                echo "<td>{$website['web_id']}</td>";
                echo "<td>{$website['web_name']}</td>";
                echo "<td>{$website['web_url']}</td>";
                echo "<td><a href='$test_url' target='_blank'>测试评论功能</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ 数据库对象不存在";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
    echo "❌ 错误：" . $e->getMessage();
    echo "</div>";
}

echo "<h2>系统信息</h2>";
echo "<p><strong>PHP版本：</strong> " . phpversion() . "</p>";
echo "<p><strong>当前时间：</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>脚本路径：</strong> " . __FILE__ . "</p>";

if (defined('ROOT_PATH')) {
    echo "<p><strong>根目录：</strong> " . ROOT_PATH . "</p>";
}

if (defined('DB_HOST')) {
    echo "<p><strong>数据库主机：</strong> " . DB_HOST . "</p>";
    echo "<p><strong>数据库名称：</strong> " . DB_NAME . "</p>";
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
