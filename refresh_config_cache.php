<?php
/**
 * 刷新配置缓存
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>刷新配置缓存</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔄 刷新配置缓存</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    $options_table = $DB->table('options');
    
    // 步骤1：显示当前配置
    echo "<h2>步骤 1: 当前数据库配置</h2>";
    
    $auto_comment_options = array(
        'auto_comment_enabled',
        'auto_comment_count', 
        'auto_comment_delay',
        'auto_comment_min_delay',
        'auto_comment_max_delay'
    );
    
    echo "<table>";
    echo "<tr><th>配置项</th><th>数据库值</th><th>当前\$options值</th></tr>";
    
    foreach ($auto_comment_options as $option_name) {
        $db_value = $DB->fetch_one("SELECT option_value FROM $options_table WHERE option_name = '$option_name'");
        $db_val = $db_value ? $db_value['option_value'] : '不存在';
        $current_val = isset($options[$option_name]) ? $options[$option_name] : '不存在';
        
        echo "<tr>";
        echo "<td>{$option_name}</td>";
        echo "<td>{$db_val}</td>";
        echo "<td>{$current_val}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 步骤2：清理缓存文件
    echo "<h2>步骤 2: 清理缓存文件</h2>";
    
    $cache_files_deleted = 0;
    $cache_dirs = array(
        ROOT_PATH . 'data/cache/',
        ROOT_PATH . 'data/compile/'
    );
    
    foreach ($cache_dirs as $cache_dir) {
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            foreach ($files as $file) {
                if (is_file($file) && unlink($file)) {
                    $cache_files_deleted++;
                }
            }
            echo "<div class='info'>清理缓存目录: {$cache_dir}</div>";
        }
    }
    
    echo "<div class='success'>✅ 删除了 {$cache_files_deleted} 个缓存文件</div>";
    
    // 步骤3：调用系统的缓存更新函数
    echo "<h2>步骤 3: 更新配置缓存</h2>";
    
    if (function_exists('update_cache')) {
        update_cache('options');
        echo "<div class='success'>✅ 调用 update_cache('options') 成功</div>";
    } else {
        echo "<div class='error'>❌ update_cache 函数不存在</div>";
    }
    
    // 步骤4：手动重新加载配置
    echo "<h2>步骤 4: 手动重新加载配置</h2>";
    
    // 清空当前配置
    $options = array();
    
    // 重新从数据库加载所有配置
    $query = $DB->query("SELECT option_name, option_value FROM $options_table");
    $loaded_count = 0;
    while ($row = $DB->fetch_array($query)) {
        $options[$row['option_name']] = $row['option_value'];
        $loaded_count++;
    }
    
    echo "<div class='success'>✅ 重新加载了 {$loaded_count} 个配置选项</div>";
    
    // 步骤5：验证自动评论配置
    echo "<h2>步骤 5: 验证自动评论配置</h2>";
    
    echo "<table>";
    echo "<tr><th>配置项</th><th>重新加载后的值</th><th>状态</th></tr>";
    
    $all_loaded = true;
    foreach ($auto_comment_options as $option_name) {
        $value = isset($options[$option_name]) ? $options[$option_name] : '不存在';
        $status = ($value !== '不存在') ? '✅ 正常' : '❌ 缺失';
        
        if ($value === '不存在') {
            $all_loaded = false;
        }
        
        echo "<tr>";
        echo "<td>{$option_name}</td>";
        echo "<td>{$value}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 步骤6：测试配置函数
    echo "<h2>步骤 6: 测试配置函数</h2>";
    
    if (file_exists(ROOT_PATH.'module/auto_comments.php')) {
        require_once(ROOT_PATH.'module/auto_comments.php');
        
        if (function_exists('get_auto_comment_config')) {
            $config = get_auto_comment_config();
            echo "<div class='success'>✅ 配置函数正常工作</div>";
            echo "<div class='info'>";
            echo "功能状态: " . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "评论数量: {$config['comment_count']} 条<br>";
            echo "延迟发布: " . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
            echo "延迟范围: {$config['min_delay']}-{$config['max_delay']} 分钟<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>❌ 配置函数不存在</div>";
            $all_loaded = false;
        }
    } else {
        echo "<div class='error'>❌ 自动评论模块文件不存在</div>";
        $all_loaded = false;
    }
    
    // 最终结果
    echo "<h2>刷新结果</h2>";
    
    if ($all_loaded) {
        echo "<div class='success'>";
        echo "<h3>🎉 配置缓存刷新成功！</h3>";
        echo "<p>所有自动评论配置选项已正确加载，现在可以正常访问后台配置页面了。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='system/option.php?opt=comment' class='btn'>前往后台配置</a>";
        echo "<a href='test_auto_comments.php' class='btn'>测试功能</a>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 配置加载不完整</h3>";
        echo "<p>部分配置选项仍然缺失，请运行修复脚本。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='fix_auto_comment_config.php' class='btn'>运行修复脚本</a>";
        echo "<a href='install_auto_comments.php' class='btn'>重新安装</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 刷新失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
