<?php
/**
 * 简化的后台页面测试
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>简化后台页面测试</h2>";

// 模拟后台环境
define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 加载基础文件
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');
require('./system/function.php');

// 加载配置
if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
require(ROOT_PATH.'data/static/options.php');
$options = $static_data;

// 加载搜索推送模块
require(APP_PATH.'module/search_push.php');

// 模拟登录用户
$myself = array(
    'user_id' => 1,
    'user_email' => '<EMAIL>',
    'login_time' => date('Y-m-d H:i:s'),
    'login_ip' => '127.0.0.1',
    'login_count' => 1,
);
$smarty->assign('myself', $myself);

// 设置页面变量
$action = 'config';
$fileurl = 'search_push.php';
$pagetitle = '搜索引擎推送配置';
$tempfile = 'search_push.html';

echo "<h3>测试配置获取</h3>";
try {
    $config = get_push_config();
    echo "✓ 配置获取成功<br/>";
    print_r($config);
} catch (Exception $e) {
    echo "✗ 配置获取失败: " . $e->getMessage() . "<br/>";
}

echo "<h3>分配Smarty变量</h3>";
$smarty->assign('action', $action);
$smarty->assign('fileurl', $fileurl);
$smarty->assign('pagetitle', $pagetitle);
$smarty->assign('config', $config);
echo "✓ 变量分配完成<br/>";

echo "<h3>测试模板渲染</h3>";
try {
    // 检查模板文件
    if (function_exists('template_exists')) {
        template_exists('search_push.html');
        echo "✓ 模板文件验证通过<br/>";
    }
    
    // 渲染模板
    echo "开始渲染模板...<br/>";
    $smarty->display('search_push.html');
    echo "<br/>✓ 模板渲染完成<br/>";
    
} catch (Exception $e) {
    echo "✗ 模板渲染失败: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . "<br/>";
    echo "错误行号: " . $e->getLine() . "<br/>";
    
    // 显示详细的错误信息
    echo "<h4>详细错误信息:</h4>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
