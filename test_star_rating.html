<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星级评分测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .star-rating {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            user-select: none;
            display: inline-block;
        }

        .star-rating .star {
            display: inline-block;
            transition: color 0.2s;
            margin-right: 3px;
            cursor: pointer;
            padding: 2px;
            line-height: 1;
            position: relative;
            z-index: 1;
        }

        .star-rating .star:hover,
        .star-rating .star.active {
            color: #ffc107;
            text-shadow: 0 0 3px rgba(255, 193, 7, 0.5);
        }

        .star-rating .star.active ~ .star {
            color: #ddd;
        }

        .rating-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }

        .rating-item label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
            vertical-align: top;
        }

        .current-value {
            margin-left: 10px;
            color: #666;
            font-size: 14px;
        }

        .debug-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .star-rating {
                font-size: 28px;
            }
            
            .star-rating .star {
                padding: 5px;
                margin-right: 5px;
            }
            
            .rating-item label {
                display: block;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <h1>星级评分功能测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>请点击下面的星星来测试评分功能：</p>
        <ul>
            <li>点击星星可以设置评分</li>
            <li>鼠标悬停可以预览评分</li>
            <li>移动端支持触摸操作</li>
            <li>当前评分值会实时显示</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>星级评分测试</h2>
        
        <div class="rating-item">
            <label>内容质量：</label>
            <div class="star-rating" data-rating="content_quality">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <span class="current-value" id="content_quality_value">当前值: 5</span>
            <input type="hidden" name="content_quality" value="5">
        </div>

        <div class="rating-item">
            <label>网站服务：</label>
            <div class="star-rating" data-rating="service_quality">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <span class="current-value" id="service_quality_value">当前值: 5</span>
            <input type="hidden" name="service_quality" value="5">
        </div>

        <div class="rating-item">
            <label>网站诚信：</label>
            <div class="star-rating" data-rating="trust_level">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <span class="current-value" id="trust_level_value">当前值: 5</span>
            <input type="hidden" name="trust_level" value="5">
        </div>
    </div>

    <div class="debug-info" id="debug-info">
        <h3>调试信息：</h3>
        <div id="debug-content">页面加载完成，等待用户操作...</div>
    </div>

    <div class="test-section">
        <h2>获取当前评分</h2>
        <button onclick="getCurrentRatings()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            获取所有评分值
        </button>
        <div id="ratings-result" style="margin-top: 10px;"></div>
    </div>

    <script>
        // 调试日志函数
        function debugLog(message) {
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            debugContent.innerHTML += `<br>[${timestamp}] ${message}`;
            console.log(message);
        }

        // 初始化星级评分
        function initStarRating() {
            debugLog('开始初始化星级评分...');
            const starRatings = document.querySelectorAll('.star-rating');
            debugLog(`找到 ${starRatings.length} 个星级评分组件`);
            
            starRatings.forEach((rating, ratingIndex) => {
                const stars = rating.querySelectorAll('.star');
                const ratingName = rating.getAttribute('data-rating');
                const ratingItem = rating.closest('.rating-item');
                const hiddenInput = ratingItem.querySelector(`input[name="${ratingName}"]`);
                const valueDisplay = document.getElementById(`${ratingName}_value`);
                
                debugLog(`初始化评分组件 ${ratingIndex + 1}: ${ratingName}`);
                
                if (!hiddenInput) {
                    debugLog(`错误: 找不到隐藏输入框 ${ratingName}`);
                    return;
                }
                
                // 默认设置为5星
                updateStars(stars, 5);
                if (valueDisplay) valueDisplay.textContent = `当前值: 5`;
                
                stars.forEach((star, index) => {
                    // 点击事件
                    star.addEventListener('click', (e) => {
                        e.preventDefault();
                        const value = index + 1;
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (valueDisplay) valueDisplay.textContent = `当前值: ${value}`;
                        debugLog(`点击设置 ${ratingName} 为 ${value} 星`);
                    });
                    
                    // 鼠标悬停事件
                    star.addEventListener('mouseover', () => {
                        updateStars(stars, index + 1);
                    });
                    
                    // 触摸事件（移动端）
                    star.addEventListener('touchstart', (e) => {
                        e.preventDefault();
                        const value = index + 1;
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (valueDisplay) valueDisplay.textContent = `当前值: ${value}`;
                        debugLog(`触摸设置 ${ratingName} 为 ${value} 星`);
                    });
                });
                
                // 鼠标离开时恢复到当前值
                rating.addEventListener('mouseleave', () => {
                    updateStars(stars, hiddenInput.value);
                });
            });
            
            debugLog('星级评分初始化完成');
        }

        // 更新星级显示
        function updateStars(stars, value) {
            stars.forEach((star, index) => {
                if (index < value) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // 获取当前所有评分
        function getCurrentRatings() {
            const ratings = {
                content_quality: document.querySelector('input[name="content_quality"]').value,
                service_quality: document.querySelector('input[name="service_quality"]').value,
                trust_level: document.querySelector('input[name="trust_level"]').value
            };
            
            const resultDiv = document.getElementById('ratings-result');
            resultDiv.innerHTML = `
                <h4>当前评分值：</h4>
                <p><strong>内容质量：</strong> ${ratings.content_quality} 星</p>
                <p><strong>网站服务：</strong> ${ratings.service_quality} 星</p>
                <p><strong>网站诚信：</strong> ${ratings.trust_level} 星</p>
            `;
            
            debugLog(`获取评分: ${JSON.stringify(ratings)}`);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM加载完成');
            initStarRating();
        });
    </script>
</body>
</html>
