<?php
/**
 * 测试表格对齐效果
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>测试表格对齐</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        
        /* 复制模板中的样式 */
        .list {
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .list td {
            padding: 8px;
            border: 1px solid #ddd;
            vertical-align: middle;
        }
        
        .list .title td {
            background: #f5f5f5;
            font-weight: bold;
            text-align: center;
            padding: 10px 8px;
        }
        
        .list .row td {
            background: #fff;
        }
        
        .list .row:hover td {
            background: #f9f9f9;
        }
        
        .content-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 8px !important;
        }
        
        .status-enabled {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #dc3545;
            font-weight: bold;
        }
        
        .list .row td a {
            color: #007cba;
            text-decoration: none;
            margin: 0 3px;
        }
        
        .list .row td a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>📊 测试表格对齐效果</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 获取一些示例数据
    $template_table = $DB->table('auto_comment_templates');
    $query = $DB->query("SELECT * FROM $template_table ORDER BY template_id DESC LIMIT 5");
    
    $templates = array();
    while ($row = $DB->fetch_array($query)) {
        $row['content_preview'] = mb_strlen($row['content']) > 50 ? mb_substr($row['content'], 0, 50) . '...' : $row['content'];
        $row['status_text'] = $row['status'] == 1 ? '<span class="status-enabled">启用</span>' : '<span class="status-disabled">禁用</span>';
        $row['create_time_text'] = date('Y-m-d H:i:s', $row['create_time']);
        $row['operate'] = '<a href="#edit">编辑</a> | <a href="#delete">删除</a>';
        $templates[] = $row;
    }
    
    echo "<h2>表格对齐效果预览</h2>";
    echo "<div class='info'>以下是使用新样式的表格效果预览：</div>";
    
    // 显示表格
    echo '<table width="100%" border="0" cellspacing="1" cellpadding="0" class="list">';
    echo '<tr class="title">';
    echo '<td width="30" align="center"><input type="checkbox"></td>';
    echo '<td width="60" align="center">ID</td>';
    echo '<td align="left">评论内容</td>';
    echo '<td width="80" align="center">分类</td>';
    echo '<td width="60" align="center">状态</td>';
    echo '<td width="120" align="center">创建时间</td>';
    echo '<td width="120" align="center">操作</td>';
    echo '</tr>';
    
    if (!empty($templates)) {
        foreach ($templates as $template) {
            echo '<tr class="row">';
            echo '<td align="center"><input type="checkbox" name="template_id[]" value="'.$template['template_id'].'"></td>';
            echo '<td align="center">'.$template['template_id'].'</td>';
            echo '<td align="left" class="content-cell">';
            echo '<div title="'.htmlspecialchars($template['content']).'">'.htmlspecialchars($template['content_preview']).'</div>';
            echo '</td>';
            echo '<td align="center">'.$template['category'].'</td>';
            echo '<td align="center">'.$template['status_text'].'</td>';
            echo '<td align="center">'.$template['create_time_text'].'</td>';
            echo '<td align="center">'.$template['operate'].'</td>';
            echo '</tr>';
        }
    } else {
        echo '<tr class="row">';
        echo '<td colspan="7" align="center" style="padding: 20px; color: #999;">暂无数据</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    
    echo "<h2>对齐效果说明</h2>";
    echo "<div class='info'>";
    echo "<h4>✅ 已优化的对齐效果：</h4>";
    echo "<ul>";
    echo "<li><strong>ID列</strong>：居中对齐，固定宽度60px</li>";
    echo "<li><strong>评论内容列</strong>：左对齐，自适应宽度，超长内容显示省略号</li>";
    echo "<li><strong>分类列</strong>：居中对齐，固定宽度80px</li>";
    echo "<li><strong>状态列</strong>：居中对齐，固定宽度60px，使用颜色区分</li>";
    echo "<li><strong>创建时间列</strong>：居中对齐，固定宽度120px</li>";
    echo "<li><strong>操作列</strong>：居中对齐，固定宽度120px</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h4>🎨 样式改进：</h4>";
    echo "<ul>";
    echo "<li>表格边框和间距优化</li>";
    echo "<li>鼠标悬停高亮效果</li>";
    echo "<li>状态使用颜色标识（绿色=启用，红色=禁用）</li>";
    echo "<li>评论内容超长时显示省略号，鼠标悬停显示完整内容</li>";
    echo "<li>操作链接样式优化</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='system/auto_comment_templates.php' class='btn' target='_blank'>🚀 查看实际效果</a>";
    echo "<a href='system/option.php?opt=comment' class='btn' target='_blank'>⚙️ 自动评论设置</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 测试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
