<?php
/**
 * 调试自动评论功能
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');
require_once(ROOT_PATH.'module/auto_comments.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>调试自动评论功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .debug-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 调试自动评论功能</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 1. 检查配置选项
    echo "<div class='debug-section'>";
    echo "<h2>1. 检查配置选项</h2>";
    
    $config = get_auto_comment_config();
    echo "<table>";
    echo "<tr><th>配置项</th><th>当前值</th><th>状态</th></tr>";
    echo "<tr><td>功能启用</td><td>" . ($config['enabled'] ? 'yes' : 'no') . "</td><td>" . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "</td></tr>";
    echo "<tr><td>评论数量</td><td>{$config['comment_count']}</td><td>✅ 正常</td></tr>";
    echo "<tr><td>延迟发布</td><td>" . ($config['delay_enabled'] ? 'yes' : 'no') . "</td><td>" . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "</td></tr>";
    echo "<tr><td>延迟范围</td><td>{$config['min_delay']}-{$config['max_delay']} 分钟</td><td>✅ 正常</td></tr>";
    echo "</table>";
    
    if (!$config['enabled']) {
        echo "<div class='error'>❌ 自动评论功能未启用！请在后台配置中启用。</div>";
    }
    echo "</div>";
    
    // 2. 检查评论模板
    echo "<div class='debug-section'>";
    echo "<h2>2. 检查评论模板</h2>";
    
    $template_table = $DB->table('auto_comment_templates');
    $template_count = $DB->get_count($template_table, '1');
    $active_count = $DB->get_count($template_table, 'status = 1');
    
    echo "<div class='info'>总模板数: {$template_count} 条，启用: {$active_count} 条</div>";
    
    if ($active_count == 0) {
        echo "<div class='error'>❌ 没有启用的评论模板！</div>";
    } else {
        $templates = get_random_comment_contents(3);
        echo "<div class='success'>✅ 评论模板正常，示例内容：</div>";
        foreach ($templates as $i => $content) {
            echo "<div style='padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;'>" . ($i+1) . ". " . htmlspecialchars($content) . "</div>";
        }
    }
    echo "</div>";
    
    // 3. 检查用户数据
    echo "<div class='debug-section'>";
    echo "<h2>3. 检查用户数据</h2>";
    
    $user_table = $DB->table('users');
    $user_count = $DB->get_count($user_table, "user_type != 'admin' AND user_status = 1");
    
    echo "<div class='info'>可用会员数: {$user_count} 个</div>";
    
    $users = get_random_comment_users(3);
    echo "<div class='success'>✅ 用户选择正常，示例用户：</div>";
    foreach ($users as $i => $user) {
        $type = $user['is_anonymous'] ? '匿名用户' : '注册会员';
        echo "<div style='padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;'>" . ($i+1) . ". {$user['user_name']} ({$type})</div>";
    }
    echo "</div>";
    
    // 4. 检查最近审核的网站
    echo "<div class='debug-section'>";
    echo "<h2>4. 检查最近审核的网站</h2>";
    
    $website_table = $DB->table('websites');
    $recent_approved = $DB->fetch_all("SELECT web_id, web_name, web_url, web_status FROM $website_table WHERE web_status = 3 ORDER BY web_id DESC LIMIT 5");
    
    if (empty($recent_approved)) {
        echo "<div class='warning'>⚠️ 没有找到已审核的网站</div>";
    } else {
        echo "<div class='success'>✅ 找到 " . count($recent_approved) . " 个已审核网站</div>";
        echo "<table>";
        echo "<tr><th>网站ID</th><th>网站名称</th><th>网站地址</th><th>评论数量</th><th>操作</th></tr>";
        
        foreach ($recent_approved as $website) {
            $comment_table = $DB->table('website_comments');
            $comment_count = $DB->get_count($comment_table, "web_id = {$website['web_id']}");
            
            echo "<tr>";
            echo "<td>{$website['web_id']}</td>";
            echo "<td>" . htmlspecialchars($website['web_name']) . "</td>";
            echo "<td>" . htmlspecialchars($website['web_url']) . "</td>";
            echo "<td>{$comment_count} 条</td>";
            echo "<td><a href='?action=test_add&web_id={$website['web_id']}' class='btn' style='padding: 5px 10px; font-size: 12px;'>测试添加</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // 5. 处理测试添加评论
    if (isset($_GET['action']) && $_GET['action'] == 'test_add' && isset($_GET['web_id'])) {
        echo "<div class='debug-section'>";
        echo "<h2>5. 测试添加评论</h2>";
        
        $test_web_id = intval($_GET['web_id']);
        
        echo "<div class='info'>正在为网站ID {$test_web_id} 添加测试评论...</div>";
        
        $result = auto_add_website_comments($test_web_id, 3);
        
        if ($result['success']) {
            echo "<div class='success'>✅ " . $result['message'] . "</div>";
            
            // 显示添加的评论
            $comment_table = $DB->table('website_comments');
            $query = $DB->query("SELECT * FROM $comment_table WHERE web_id = $test_web_id ORDER BY create_time DESC LIMIT 5");
            
            echo "<h3>最新评论：</h3>";
            echo "<table>";
            echo "<tr><th>用户</th><th>评分</th><th>评论内容</th><th>时间</th></tr>";
            
            while ($comment = $DB->fetch_array($query)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($comment['user_name']) . "</td>";
                echo "<td>{$comment['content_quality']}★ {$comment['service_quality']}★ {$comment['trust_level']}★</td>";
                echo "<td>" . htmlspecialchars($comment['comment_content']) . "</td>";
                echo "<td>" . date('Y-m-d H:i:s', $comment['create_time']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>❌ " . $result['message'] . "</div>";
        }
        echo "</div>";
    }
    
    // 6. 检查错误日志
    echo "<div class='debug-section'>";
    echo "<h2>6. 检查错误日志</h2>";
    
    $log_file = ROOT_PATH . 'data/logs/error.log';
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $auto_comment_errors = array();
        
        $lines = explode("\n", $log_content);
        foreach ($lines as $line) {
            if (strpos($line, '自动评论') !== false) {
                $auto_comment_errors[] = $line;
            }
        }
        
        if (!empty($auto_comment_errors)) {
            echo "<div class='warning'>⚠️ 发现自动评论相关错误：</div>";
            foreach (array_slice($auto_comment_errors, -5) as $error) {
                echo "<div style='padding: 5px; background: #fff3cd; margin: 5px 0; border-radius: 3px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($error) . "</div>";
            }
        } else {
            echo "<div class='success'>✅ 没有发现自动评论相关错误</div>";
        }
    } else {
        echo "<div class='info'>ℹ️ 错误日志文件不存在</div>";
    }
    echo "</div>";
    
    // 7. 诊断建议
    echo "<div class='debug-section'>";
    echo "<h2>7. 诊断建议</h2>";
    
    $issues = array();
    $suggestions = array();
    
    if (!$config['enabled']) {
        $issues[] = "自动评论功能未启用";
        $suggestions[] = "前往后台 → 系统设置 → 自动评论设置，启用功能";
    }
    
    if ($active_count == 0) {
        $issues[] = "没有启用的评论模板";
        $suggestions[] = "前往后台 → 系统设置 → 评论模板管理，启用一些模板";
    }
    
    if (empty($recent_approved)) {
        $issues[] = "没有已审核的网站进行测试";
        $suggestions[] = "先审核通过一些网站，然后观察是否自动添加评论";
    }
    
    if (empty($issues)) {
        echo "<div class='success'>";
        echo "<h3>✅ 功能配置正常</h3>";
        echo "<p>自动评论功能的所有组件都已正确配置。如果仍然没有自动添加评论，请：</p>";
        echo "<ol>";
        echo "<li>确保网站状态确实从待审核(2)变为已审核(3)</li>";
        echo "<li>检查PHP错误日志是否有相关错误信息</li>";
        echo "<li>使用上面的'测试添加'按钮手动测试功能</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 发现以下问题：</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
        echo "<h3>🔧 建议解决方案：</h3>";
        echo "<ol>";
        foreach ($suggestions as $suggestion) {
            echo "<li>{$suggestion}</li>";
        }
        echo "</ol>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='system/option.php?opt=comment' class='btn' target='_blank'>⚙️ 后台配置</a>";
    echo "<a href='system/auto_comment_templates.php' class='btn' target='_blank'>📝 模板管理</a>";
    echo "<a href='test_auto_comments.php' class='btn' target='_blank'>🧪 功能测试</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 调试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
