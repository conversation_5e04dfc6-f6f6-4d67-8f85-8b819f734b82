# 分页SEO优化功能说明

## 概述

本次优化为网站的所有分页页面添加了完整的SEO优化功能，帮助搜索引擎更好地理解和索引分页内容。

## 已优化的页面模块

✅ **网站目录** (webdir.php) - 网站分类浏览页面
✅ **文章资讯** (article.php) - 文章列表页面
✅ **VIP站点** (vip_list.php) - VIP网站列表页面
✅ **黑名单** (blacklist.php) - 违规网站黑名单页面
✅ **待审核** (pending.php) - 等待审核的网站页面
✅ **审核不通过** (rejected.php) - 审核失败的网站页面
✅ **最新收录** (update.php) - 最近收录的网站页面
✅ **数据归档** (archives.php) - 按时间归档的网站页面
✅ **链接交换** (weblink.php) - 友情链接交换页面
✅ **搜索结果** (search.php) - 网站和文章搜索结果页面

## 优化内容

### 1. 页面标题优化
- **功能**: 在分页页面的标题中添加页码信息
- **效果**: 
  - 第1页: `网站目录 - 95目录网`
  - 第2页: `网站目录(第2页) - 95目录网`
  - 分类页第2页: `科技网站大全(第2页) - 科技网站目录 - 95目录网`

### 2. Meta标签优化
- **Robots标签**: 
  - 第1页: `index,follow` (允许索引和跟踪链接)
  - 其他页面: `noindex,follow` (不索引但跟踪链接，避免重复内容)
- **Description标签**: 在描述中包含当前页码信息

### 3. Canonical链接优化
- **功能**: 为每个分页页面设置正确的canonical链接
- **效果**: 告诉搜索引擎当前页面的规范URL
- **示例**: 
  ```html
  <link rel="canonical" href="https://www.95dir.com?mod=webdir&page=2" />
  ```

### 4. Prev/Next链接
- **功能**: 添加rel="prev"和rel="next"链接标签
- **效果**: 帮助搜索引擎理解分页序列关系
- **示例**:
  ```html
  <link rel="prev" href="https://www.95dir.com?mod=webdir&page=1" />
  <link rel="next" href="https://www.95dir.com?mod=webdir&page=3" />
  ```

### 5. 结构化数据优化
- **功能**: 添加分页相关的JSON-LD结构化数据
- **包含信息**:
  - 当前页码
  - 总页数
  - 每页项目数
  - 总项目数
  - 上一页/下一页链接

## 涉及的文件

### 后端文件
1. **module/webdir.php** - 网站目录页面分页SEO优化
2. **module/article.php** - 文章列表页面分页SEO优化
3. **module/vip_list.php** - VIP页面分页SEO优化
4. **module/blacklist.php** - 黑名单页面分页SEO优化
5. **module/pending.php** - 待审核页面分页SEO优化
6. **module/rejected.php** - 审核不通过页面分页SEO优化
7. **module/update.php** - 最新收录页面分页SEO优化
8. **module/archives.php** - 数据归档页面分页SEO优化
9. **module/weblink.php** - 链接交换页面分页SEO优化
10. **module/search.php** - 搜索结果页面分页SEO优化

### 前端模板文件
1. **themes/default/webdir.html**
   - 添加canonical、prev/next链接
   - 更新结构化数据支持分页
   - 动态robots meta标签

2. **themes/default/article.html**
   - 添加分页SEO meta标签
   - canonical和prev/next链接

3. **themes/default/vip_list.html** - VIP页面分页SEO优化
4. **themes/default/blacklist.html** - 黑名单页面分页SEO优化
5. **themes/default/pending.html** - 待审核页面分页SEO优化
6. **themes/default/rejected.html** - 审核不通过页面分页SEO优化
7. **themes/default/update.html** - 最新收录页面分页SEO优化
8. **themes/default/archives.html** - 数据归档页面分页SEO优化
9. **themes/default/weblink.html** - 链接交换页面分页SEO优化
10. **themes/default/search.html** - 搜索结果页面分页SEO优化

## 核心函数说明

### get_pagination_seo_data()
```php
function get_pagination_seo_data($module, $total, $curpage, $pagesize, $params = array())
```

**参数说明**:
- `$module`: 模块名称 (如 'webdir', 'article', 'vip_list')
- `$total`: 总记录数
- `$curpage`: 当前页码
- `$pagesize`: 每页显示数量
- `$params`: 额外参数数组 (如分类ID等)

**返回数据**:
```php
array(
    'curpage' => 2,                    // 当前页码
    'total_pages' => 25,               // 总页数
    'canonical_url' => 'https://...',  // canonical URL
    'prev_url' => 'https://...',       // 上一页URL
    'next_url' => 'https://...',       // 下一页URL
    'seo_title_suffix' => '(第2页)',   // 标题后缀
    'robots_content' => 'noindex,follow' // robots标签内容
)
```

## SEO效果

### 1. 避免重复内容问题
- 通过noindex标签避免分页页面被重复索引
- 使用canonical链接指向正确的页面

### 2. 改善爬虫理解
- Prev/Next链接帮助搜索引擎理解分页结构
- 结构化数据提供详细的分页信息

### 3. 提升用户体验
- 清晰的页面标题显示当前位置
- 完整的分页导航信息

## 测试方法

1. **访问测试页面**: `test_pagination_seo.php`
2. **检查页面源码**: 查看meta标签和链接标签
3. **使用SEO工具**: 
   - Google Search Console
   - Screaming Frog SEO Spider
   - SEMrush Site Audit

## 最佳实践建议

### 1. 监控索引状态
- 定期检查Google Search Console中的索引状态
- 确认只有第1页被索引，其他页面正确设置为noindex

### 2. 页面加载速度
- 分页页面应保持快速加载
- 考虑使用缓存机制

### 3. 内容质量
- 确保每页都有足够的有价值内容
- 避免页面内容过少

### 4. 移动端优化
- 确保分页在移动端正常工作
- 考虑无限滚动等移动端友好的分页方式

## 注意事项

1. **缓存清理**: 修改后需要清理模板缓存
2. **URL一致性**: 确保分页URL格式保持一致
3. **性能影响**: 分页SEO优化对性能影响很小
4. **兼容性**: 与现有URL重写规则兼容

## 扩展建议

1. **添加更多模块**: 可以为其他有分页的模块添加类似优化
2. **国际化支持**: 支持多语言的分页SEO优化
3. **AMP支持**: 为AMP页面添加分页SEO优化
4. **API接口**: 提供分页SEO数据的API接口

通过以上优化，网站的分页页面将更加SEO友好，有助于提升搜索引擎排名和用户体验。
