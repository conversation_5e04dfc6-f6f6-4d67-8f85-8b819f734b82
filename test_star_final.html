<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星级评分最终测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .star-rating {
            font-size: 24px;
            cursor: pointer;
            user-select: none;
            display: inline-block;
        }

        .star-rating .star {
            display: inline-block;
            transition: color 0.2s;
            margin-right: 3px;
            cursor: pointer;
            padding: 2px;
            line-height: 1;
            position: relative;
            z-index: 1;
            color: #ddd;
        }

        .star-rating .star:hover,
        .star-rating .star.active {
            color: #ffc107 !important;
            text-shadow: 0 0 3px rgba(255, 193, 7, 0.5);
        }

        .rating-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }

        .rating-item label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
            vertical-align: top;
        }

        .current-value {
            margin-left: 10px;
            color: #666;
            font-size: 14px;
        }

        .test-button {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .star-rating {
                font-size: 28px;
            }

            .star-rating .star {
                padding: 5px;
                margin-right: 5px;
            }
        }

        @media (max-width: 480px) {
            .star-rating {
                font-size: 32px;
            }

            .star-rating .star {
                padding: 8px;
                margin-right: 8px;
            }
            
            .rating-item label {
                display: block;
                margin-bottom: 8px;
                width: 100%;
            }
        }

        .status-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>星级评分最终测试</h1>
    
    <div class="test-section">
        <h2>修复内容</h2>
        <ul>
            <li>✅ 添加了480px媒体查询</li>
            <li>✅ 移除了"重新初始化星级"按钮</li>
            <li>✅ 默认显示5颗灰色星星</li>
            <li>✅ 只有点击后才点亮对应的星星</li>
            <li>✅ 第5颗星点击问题已修复</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>星级评分测试</h2>
        
        <div class="rating-item">
            <label>内容质量：</label>
            <div class="star-rating" data-rating="content_quality">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <span class="current-value" id="content_quality_value">当前值: 未评分</span>
            <input type="hidden" name="content_quality" value="0">
        </div>

        <div class="rating-item">
            <label>网站服务：</label>
            <div class="star-rating" data-rating="service_quality">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <span class="current-value" id="service_quality_value">当前值: 未评分</span>
            <input type="hidden" name="service_quality" value="0">
        </div>

        <div class="rating-item">
            <label>网站诚信：</label>
            <div class="star-rating" data-rating="trust_level">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
            </div>
            <span class="current-value" id="trust_level_value">当前值: 未评分</span>
            <input type="hidden" name="trust_level" value="0">
        </div>
    </div>

    <div class="test-section">
        <h2>测试操作</h2>
        <button class="test-button" onclick="testFifthStar()">测试第5颗星</button>
        <button class="test-button" onclick="resetAllRatings()">重置为灰色</button>
        <button class="test-button" onclick="getCurrentRatings()">获取当前评分</button>
    </div>

    <div class="status-info" id="status-info">
        <strong>状态：</strong>页面已加载，所有星星默认为灰色状态。请点击星星进行测试。
    </div>

    <script>
        // 更新星级显示
        function updateStars(stars, value) {
            stars.forEach((star, index) => {
                // 先移除所有active类
                star.classList.remove('active');
                // 然后为应该点亮的星星添加active类
                if (index < value) {
                    star.classList.add('active');
                }
            });
        }

        // 初始化星级评分
        function initStarRating() {
            const starRatings = document.querySelectorAll('.star-rating');
            
            starRatings.forEach((rating) => {
                const stars = rating.querySelectorAll('.star');
                const ratingName = rating.getAttribute('data-rating');
                const ratingItem = rating.closest('.rating-item');
                const hiddenInput = ratingItem.querySelector(`input[name="${ratingName}"]`);
                const valueDisplay = document.getElementById(`${ratingName}_value`);
                
                // 默认显示为灰色星星，不点亮任何星星
                hiddenInput.value = 0;
                if (valueDisplay) valueDisplay.textContent = `当前值: 未评分`;
                
                stars.forEach((star, index) => {
                    // 点击事件
                    star.addEventListener('click', (e) => {
                        e.preventDefault();
                        const value = index + 1;
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (valueDisplay) valueDisplay.textContent = `当前值: ${value}星`;
                        updateStatus(`点击设置 ${ratingName} 为 ${value} 星`);
                    });
                    
                    // 鼠标悬停事件
                    star.addEventListener('mouseover', () => {
                        updateStars(stars, index + 1);
                    });
                    
                    // 触摸事件（移动端）
                    star.addEventListener('touchstart', (e) => {
                        e.preventDefault();
                        const value = index + 1;
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (valueDisplay) valueDisplay.textContent = `当前值: ${value}星`;
                        updateStatus(`触摸设置 ${ratingName} 为 ${value} 星`);
                    });
                });
                
                // 鼠标离开时恢复到当前值
                rating.addEventListener('mouseleave', () => {
                    const currentValue = parseInt(hiddenInput.value) || 0;
                    updateStars(stars, currentValue);
                });
            });
        }

        // 更新状态信息
        function updateStatus(message) {
            const statusInfo = document.getElementById('status-info');
            const timestamp = new Date().toLocaleTimeString();
            statusInfo.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        }

        // 测试第5颗星
        function testFifthStar() {
            updateStatus('开始测试第5颗星...');
            const ratings = ['content_quality', 'service_quality', 'trust_level'];
            
            ratings.forEach((ratingName, index) => {
                setTimeout(() => {
                    const stars = document.querySelectorAll(`.star-rating[data-rating="${ratingName}"] .star`);
                    const hiddenInput = document.querySelector(`input[name="${ratingName}"]`);
                    const valueDisplay = document.getElementById(`${ratingName}_value`);
                    
                    // 设置为5星
                    updateStars(stars, 5);
                    hiddenInput.value = 5;
                    if (valueDisplay) valueDisplay.textContent = `当前值: 5星`;
                    updateStatus(`${ratingName} 设置为 5 星 - 检查第5颗星是否正确点亮`);
                }, index * 1000);
            });
        }

        // 重置所有评分
        function resetAllRatings() {
            updateStatus('重置所有评分为灰色状态...');
            const ratings = ['content_quality', 'service_quality', 'trust_level'];
            
            ratings.forEach(ratingName => {
                const stars = document.querySelectorAll(`.star-rating[data-rating="${ratingName}"] .star`);
                const hiddenInput = document.querySelector(`input[name="${ratingName}"]`);
                const valueDisplay = document.getElementById(`${ratingName}_value`);
                
                updateStars(stars, 0);
                hiddenInput.value = 0;
                if (valueDisplay) valueDisplay.textContent = `当前值: 未评分`;
            });
            updateStatus('所有评分已重置为灰色状态');
        }

        // 获取当前所有评分
        function getCurrentRatings() {
            const ratings = {
                content_quality: document.querySelector('input[name="content_quality"]').value,
                service_quality: document.querySelector('input[name="service_quality"]').value,
                trust_level: document.querySelector('input[name="trust_level"]').value
            };
            
            const message = `当前评分 - 内容质量: ${ratings.content_quality}星, 网站服务: ${ratings.service_quality}星, 网站诚信: ${ratings.trust_level}星`;
            updateStatus(message);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initStarRating();
            updateStatus('页面初始化完成，所有星星默认为灰色状态');
        });
    </script>
</body>
</html>
