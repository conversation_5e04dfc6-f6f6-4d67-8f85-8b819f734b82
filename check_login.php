<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>登录状态检查</h2>";

// 1. 检查Cookie
echo "<h3>1. <PERSON><PERSON>检查</h3>";
if (isset($_COOKIE['user_auth'])) {
    echo "✓ 用户认证Cookie存在<br/>";
    echo "Cookie值: " . substr($_COOKIE['user_auth'], 0, 20) . "...<br/>";
} else {
    echo "✗ 用户认证Cookie不存在<br/>";
    echo "<strong>这是导致白屏的主要原因！</strong><br/>";
}

// 2. 检查Session
echo "<h3>2. Session检查</h3>";
session_start();
if (isset($_SESSION['user_id'])) {
    echo "✓ Session中有用户ID: " . $_SESSION['user_id'] . "<br/>";
} else {
    echo "⚠ Session中没有用户ID<br/>";
}

// 3. 尝试模拟登录检查
echo "<h3>3. 模拟登录检查</h3>";

define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

try {
    require(APP_PATH.'init.php');
    require('./system/load.php');
    
    echo "✓ 系统文件加载成功<br/>";
    
    if (isset($myself) && !empty($myself)) {
        echo "✓ 用户已登录<br/>";
        echo "用户ID: " . $myself['user_id'] . "<br/>";
        echo "用户邮箱: " . $myself['user_email'] . "<br/>";
    } else {
        echo "✗ 用户未登录或登录信息无效<br/>";
        echo "<strong>这就是导致search_push.php白屏的原因！</strong><br/>";
    }
    
} catch (Exception $e) {
    echo "✗ 系统加载失败: " . $e->getMessage() . "<br/>";
}

// 4. 提供解决方案
echo "<h3>4. 解决方案</h3>";
echo "<p><strong>如果您看到登录相关的错误，请按以下步骤操作：</strong></p>";
echo "<ol>";
echo "<li><a href='system/login.php' target='_blank'>点击这里登录后台管理系统</a></li>";
echo "<li>登录成功后，再访问 <a href='system/search_push.php' target='_blank'>搜索引擎推送页面</a></li>";
echo "<li>如果仍然有问题，请清除浏览器Cookie和缓存后重新登录</li>";
echo "</ol>";

echo "<h3>5. 其他测试链接</h3>";
echo "<ul>";
echo "<li><a href='system/main.php' target='_blank'>后台首页</a></li>";
echo "<li><a href='system/feedback.php' target='_blank'>意见反馈（对比测试）</a></li>";
echo "<li><a href='system/search_push_simple_test.php' target='_blank'>简单测试页面</a></li>";
echo "</ul>";
?>
