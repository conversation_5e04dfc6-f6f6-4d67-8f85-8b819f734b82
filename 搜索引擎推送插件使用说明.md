# 搜索引擎推送插件使用说明

## 功能介绍

本插件为95分类目录网站提供了自动推送到百度、谷歌、必应搜索引擎的功能，帮助网站快速被搜索引擎收录和索引。

## 主要功能

1. **API配置管理** - 配置各搜索引擎的API密钥和Token
2. **手动推送** - 手动选择搜索引擎进行URL推送
3. **网站地图生成** - 自动生成符合标准的sitemap.xml文件
4. **自动推送设置** - 配置定时自动推送功能
5. **推送日志** - 查看历史推送记录和结果

## 安装说明

插件已经集成到后台管理系统中，可以在"辅助功能"菜单下找到"搜索引擎推送"选项。

### 文件列表

- `system/search_push.php` - 后台管理文件
- `module/search_push.php` - 推送功能模块
- `themes/system/search_push.html` - 前端模板文件
- `cron_auto_push.php` - 自动推送定时任务文件

## 配置说明

### 1. 百度推送配置

1. 登录百度站长平台 (https://ziyuan.baidu.com/)
2. 添加并验证您的网站
3. 进入"数据引入" → "链接提交" → "主动推送"
4. 获取推送Token
5. 在插件配置页面填入Token

### 2. 谷歌推送配置

1. 登录Google Cloud Console (https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用"Google Search Console API"
4. 创建API密钥
5. 在插件配置页面填入API密钥

### 3. 必应推送配置

1. 登录Bing Webmaster Tools (https://www.bing.com/webmasters/)
2. 添加并验证您的网站
3. 进入"Settings" → "API Access"
4. 生成API密钥
5. 在插件配置页面填入API密钥

## 使用方法

### 手动推送

1. 进入后台 → 辅助功能 → 搜索引擎推送
2. 点击"手动推送"标签
3. 选择要推送的搜索引擎
4. 选择推送URL数量
5. 点击"开始推送"按钮

### 自动推送设置

1. 进入"自动推送"标签
2. 启用自动推送功能
3. 选择要推送的搜索引擎
4. 设置推送间隔时间
5. 保存设置

### 配置定时任务

为了使自动推送功能正常工作，需要在服务器上配置crontab定时任务：

```bash
# 编辑crontab
crontab -e

# 添加以下行（每24小时执行一次）
0 */24 * * * /usr/bin/php /path/to/your/website/cron_auto_push.php

# 或者每6小时执行一次
0 */6 * * * /usr/bin/php /path/to/your/website/cron_auto_push.php
```

请将 `/path/to/your/website/` 替换为您网站的实际路径。

## 推送策略

### URL选择策略

插件会自动选择以下类型的URL进行推送：

1. 网站首页
2. 分类页面
3. 网站详情页（最新1000个）
4. 文章页面（最新500个）

### 推送频率建议

- **百度**：建议每天推送，单次最多2000个URL
- **谷歌**：建议每周推送sitemap，无URL数量限制
- **必应**：建议每天推送，单次最多10个URL

## 注意事项

1. **API配额限制**：各搜索引擎都有API调用频率和数量限制，请合理设置推送频率
2. **网络环境**：确保服务器能够正常访问各搜索引擎的API接口
3. **SSL证书**：谷歌API需要HTTPS连接，确保服务器支持SSL
4. **权限验证**：确保在各搜索引擎平台已验证网站所有权

## 故障排除

### 安装检查

如果插件无法正常工作，请先运行以下检查脚本：

1. **功能测试**：访问 `http://您的域名/test_search_push.php`
2. **修复脚本**：访问 `http://您的域名/fix_search_push.php`

### 常见问题

1. **点击菜单没有反应**
   - 检查文件是否在正确位置
   - 确认 `system/search_push.php` 文件存在
   - 检查文件权限是否正确

2. **推送失败**
   - 检查API密钥是否正确
   - 检查网络连接是否正常
   - 查看推送日志获取详细错误信息
   - 确认服务器支持curl扩展

3. **自动推送不工作**
   - 检查crontab是否正确配置
   - 检查PHP路径是否正确
   - 查看服务器错误日志
   - 确认自动推送功能已启用

4. **网站地图无法访问**
   - 检查网站根目录写入权限
   - 确保sitemap.xml文件已生成
   - 检查文件是否存在于网站根目录

5. **模板显示错误**
   - 检查 `themes/system/search_push.html` 文件是否存在
   - 确认Smarty模板语法正确
   - 清除模板缓存

### 文件权限检查

确保以下目录具有写入权限：
- `data/static/` (配置文件)
- 网站根目录 (sitemap.xml)
- `data/compile/` (模板编译)

### 日志查看

在"推送日志"页面可以查看详细的推送记录，包括：
- 推送时间
- 推送搜索引擎
- 推送URL数量
- 推送结果状态
- 错误信息

## 技术支持

如果在使用过程中遇到问题，可以：

1. 查看推送日志获取错误信息
2. 检查服务器错误日志
3. 确认API配置是否正确
4. 测试网络连接是否正常

## 更新日志

### v1.0.0
- 初始版本发布
- 支持百度、谷歌、必应三大搜索引擎
- 提供手动推送和自动推送功能
- 集成网站地图生成功能
- 完整的推送日志记录

---

**注意**：本插件仅适用于95分类目录系统，使用前请确保已正确配置各搜索引擎的API密钥。
