<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论违规检测和管理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .feature-list {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .feature-list h4 {
            margin-top: 0;
            color: #0066cc;
        }

        .feature-list ul {
            margin-bottom: 0;
        }

        .feature-list li {
            margin-bottom: 5px;
        }

        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .admin-button {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }

        .admin-button:hover {
            background: #c82333;
        }

        .violation-demo {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .violation-demo h4 {
            color: #856404;
            margin-top: 0;
        }

        .hidden-content {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            font-style: italic;
        }

        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .status-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .warning-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>评论违规检测和管理功能测试</h1>
    
    <div class="test-section">
        <h2>功能概述</h2>
        <p>本次更新为评论系统添加了违规内容检测和管理员删除功能，提升了内容管理的安全性和便利性。</p>
        
        <div class="feature-list">
            <h4>✅ 新增功能</h4>
            <ul>
                <li><strong>违规内容检测：</strong>自动检测评论中的违规关键词</li>
                <li><strong>内容隐藏：</strong>违规评论自动显示为"[此评论包含违规内容，已被隐藏]"</li>
                <li><strong>管理员权限：</strong>自动检测当前用户是否为管理员</li>
                <li><strong>删除功能：</strong>管理员可直接在评论区删除违规评论</li>
                <li><strong>级联删除：</strong>删除主评论时，其所有回复也会被删除</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>违规内容检测演示</h2>
        
        <div class="violation-demo">
            <h4>违规内容示例</h4>
            <p>以下是违规内容的显示效果：</p>
            <div class="hidden-content">
                [此评论包含违规内容，已被隐藏]
            </div>
            <p><small>原始内容包含违规关键词时会被自动隐藏</small></p>
        </div>

        <div class="feature-list">
            <h4>检测机制</h4>
            <ul>
                <li>使用系统配置的违规关键词列表</li>
                <li>检测范围包括主评论和回复</li>
                <li>违规内容仅对普通用户隐藏，管理员可查看原始内容</li>
                <li>检测在评论显示时进行，不影响数据库存储</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>管理员删除功能</h2>
        
        <p>管理员登录后，在每条评论和回复旁边会显示红色的"删除"按钮：</p>
        
        <div style="background: white; padding: 15px; border: 1px solid #ddd; border-radius: 4px; margin: 15px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <span><strong>用户昵称</strong></span>
                <div>
                    <span style="color: #666; font-size: 12px;">2024-01-01 12:00:00</span>
                    <button class="admin-button">删除</button>
                </div>
            </div>
            <div>这是一条示例评论内容...</div>
        </div>

        <div class="feature-list">
            <h4>删除功能特性</h4>
            <ul>
                <li>只有管理员可以看到删除按钮</li>
                <li>删除前会弹出确认对话框</li>
                <li>删除操作会将评论状态设为隐藏（软删除）</li>
                <li>删除主评论时，其所有回复也会被删除</li>
                <li>删除后自动刷新评论列表</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>技术实现</h2>
        
        <div class="code-block">
// 违规内容检测函数
function check_comment_violation($content) {
    global $options;
    
    // 使用系统的违规词检测
    if (!empty($options['filter_words'])) {
        if (!censor_words($options['filter_words'], $content)) {
            return true;
        }
    }
    
    return false;
}
        </div>

        <div class="code-block">
// 管理员权限检查
function check_admin_permission() {
    // 检查管理员Cookie
    if (!isset($_COOKIE['user_auth'])) {
        return false;
    }
    
    // 验证管理员身份
    global $DB;
    $user = $DB->fetch_one("SELECT user_id, user_pass FROM " . 
        $DB->table('users') . " WHERE user_type='admin' AND user_id='$user_id'");
    
    return ($user && $user['user_pass'] == $user_pass);
}
        </div>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        
        <div class="status-info">
            <h4>普通用户测试</h4>
            <ol>
                <li>访问任意网站详情页</li>
                <li>查看评论区，违规评论应显示为隐藏状态</li>
                <li>确认没有删除按钮显示</li>
            </ol>
        </div>

        <div class="warning-info">
            <h4>管理员测试</h4>
            <ol>
                <li>使用管理员账号登录后台</li>
                <li>访问网站详情页</li>
                <li>确认每条评论旁边有红色删除按钮</li>
                <li>点击删除按钮测试删除功能</li>
                <li>确认删除后评论消失</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>快速测试链接</h2>
        
        <a href="/" class="test-button">访问首页</a>
        <a href="system/login.php" class="test-button" target="_blank">管理员登录</a>
        <a href="?mod=siteinfo&web_id=1" class="test-button">查看网站详情</a>
        
        <div style="margin-top: 15px;">
            <p><strong>测试建议：</strong></p>
            <ul>
                <li>先以普通用户身份测试违规内容隐藏功能</li>
                <li>然后登录管理员账号测试删除功能</li>
                <li>测试删除主评论时回复是否一并删除</li>
                <li>确认删除操作的权限控制是否有效</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>注意事项</h2>
        
        <div class="warning-info">
            <ul>
                <li>违规词列表在后台"选项设置"中配置</li>
                <li>删除操作是软删除，数据库中记录仍存在但状态为隐藏</li>
                <li>管理员权限基于Cookie验证，确保管理员已正确登录</li>
                <li>建议定期检查和清理违规评论</li>
            </ul>
        </div>
    </div>

</body>
</html>
