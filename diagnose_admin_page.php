<?php
/**
 * 诊断后台页面问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>后台页面诊断</h2>";

// 1. 模拟后台环境
echo "<h3>1. 模拟后台环境</h3>";

define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "ROOT_PATH: " . ROOT_PATH . "<br/>";
echo "APP_PATH: " . APP_PATH . "<br/>";

// 2. 加载必要文件
echo "<h3>2. 加载系统文件</h3>";

try {
    require(APP_PATH.'init.php');
    echo "✓ init.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ init.php 加载失败: " . $e->getMessage() . "<br/>";
    exit;
}

try {
    require(APP_PATH.'module/static.php');
    echo "✓ static.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ static.php 加载失败: " . $e->getMessage() . "<br/>";
}

try {
    require('./system/function.php');
    echo "✓ function.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ function.php 加载失败: " . $e->getMessage() . "<br/>";
}

// 3. 检查数据库和Smarty
echo "<h3>3. 检查核心对象</h3>";

if (isset($DB)) {
    echo "✓ 数据库对象存在<br/>";
} else {
    echo "✗ 数据库对象不存在<br/>";
}

if (isset($smarty)) {
    echo "✓ Smarty对象存在<br/>";
    echo "Smarty版本: " . (defined('Smarty::SMARTY_VERSION') ? Smarty::SMARTY_VERSION : '未知') . "<br/>";
} else {
    echo "✗ Smarty对象不存在<br/>";
}

// 4. 模拟用户登录
echo "<h3>4. 模拟用户登录检查</h3>";

// 创建一个虚拟的登录用户
$myself = array(
    'user_id' => 1,
    'user_email' => '<EMAIL>',
    'login_time' => date('Y-m-d H:i:s'),
    'login_ip' => '127.0.0.1',
    'login_count' => 1,
);

if (isset($smarty)) {
    $smarty->assign('myself', $myself);
    echo "✓ 用户信息已分配给Smarty<br/>";
}

// 5. 加载配置文件
echo "<h3>5. 加载配置文件</h3>";

try {
    if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
    require(ROOT_PATH.'data/static/options.php');
    
    if (isset($static_data)) {
        echo "✓ 配置文件加载成功<br/>";
        $options = $static_data;
        echo "网站URL: " . $options['site_url'] . "<br/>";
    } else {
        echo "✗ 配置数据不存在<br/>";
    }
} catch (Exception $e) {
    echo "✗ 配置文件加载失败: " . $e->getMessage() . "<br/>";
}

// 6. 加载搜索推送模块
echo "<h3>6. 加载搜索推送模块</h3>";

try {
    require(APP_PATH.'module/search_push.php');
    echo "✓ 搜索推送模块加载成功<br/>";
    
    // 测试函数
    if (function_exists('get_push_config')) {
        $config = get_push_config();
        echo "✓ get_push_config 函数正常<br/>";
    } else {
        echo "✗ get_push_config 函数不存在<br/>";
    }
    
} catch (Exception $e) {
    echo "✗ 搜索推送模块加载失败: " . $e->getMessage() . "<br/>";
}

// 7. 测试模板文件
echo "<h3>7. 测试模板文件</h3>";

$template_file = ROOT_PATH . 'themes/system/search_push.html';
if (file_exists($template_file)) {
    echo "✓ 模板文件存在<br/>";
    
    // 检查模板内容
    $content = file_get_contents($template_file);
    if (strpos($content, '{#include file="header.html"#}') !== false) {
        echo "✓ 模板语法正确<br/>";
    } else {
        echo "⚠ 模板语法可能有问题<br/>";
    }
    
    // 检查模板是否可以被Smarty处理
    if (isset($smarty)) {
        try {
            if (function_exists('template_exists')) {
                template_exists('search_push.html');
                echo "✓ 模板文件可以被Smarty识别<br/>";
            } else {
                echo "⚠ template_exists 函数不存在<br/>";
            }
        } catch (Exception $e) {
            echo "✗ 模板文件检查失败: " . $e->getMessage() . "<br/>";
        }
    }
} else {
    echo "✗ 模板文件不存在<br/>";
}

// 8. 模拟完整的页面处理
echo "<h3>8. 模拟页面处理</h3>";

if (isset($smarty) && function_exists('get_push_config')) {
    try {
        // 设置页面变量
        $action = 'config';
        $fileurl = 'search_push.php';
        $pagetitle = '搜索引擎推送配置';
        $tempfile = 'search_push.html';
        
        // 获取配置
        $config = get_push_config();
        
        // 分配变量给Smarty
        $smarty->assign('action', $action);
        $smarty->assign('fileurl', $fileurl);
        $smarty->assign('pagetitle', $pagetitle);
        $smarty->assign('config', $config);
        
        echo "✓ 页面变量设置成功<br/>";
        echo "Action: " . $action . "<br/>";
        echo "Page Title: " . $pagetitle . "<br/>";
        echo "Template: " . $tempfile . "<br/>";
        
        // 尝试渲染模板（但不输出）
        if (file_exists($template_file)) {
            echo "✓ 准备渲染模板<br/>";
            echo "<strong>如果到这里都正常，说明代码没有问题，可能是权限或缓存问题</strong><br/>";
        }
        
    } catch (Exception $e) {
        echo "✗ 页面处理失败: " . $e->getMessage() . "<br/>";
    }
}

// 9. 检查可能的问题
echo "<h3>9. 可能的问题检查</h3>";

// 检查目录权限
$dirs = array(
    ROOT_PATH . 'data/compile/system/',
    ROOT_PATH . 'data/cache/',
    ROOT_PATH . 'themes/system/'
);

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✓ 目录 " . basename($dir) . " 可写<br/>";
        } else {
            echo "⚠ 目录 " . basename($dir) . " 不可写<br/>";
        }
    } else {
        echo "⚠ 目录 " . basename($dir) . " 不存在<br/>";
    }
}

// 10. 提供解决方案
echo "<h3>10. 解决方案</h3>";
echo "<p><strong>如果上述检查都通过，但后台页面仍然空白，请尝试：</strong></p>";
echo "<ol>";
echo "<li>清除浏览器缓存</li>";
echo "<li>清除Smarty模板缓存（删除 data/compile/system/ 目录下的文件）</li>";
echo "<li>检查PHP错误日志</li>";
echo "<li>确保已经正确登录后台管理系统</li>";
echo "<li>尝试访问其他后台页面确认后台系统正常</li>";
echo "</ol>";

echo "<p><strong>测试链接：</strong></p>";
echo "<a href='system/search_push.php' target='_blank'>直接访问搜索引擎推送页面</a><br/>";
echo "<a href='system/login.php' target='_blank'>后台登录页面</a><br/>";
echo "<a href='system/main.php' target='_blank'>后台首页</a><br/>";
?>
