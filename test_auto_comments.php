<?php
/**
 * 自动评论功能测试脚本
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');
require_once(ROOT_PATH.'module/auto_comments.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>自动评论功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        .comment-preview { background: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 3px solid #007bff; }
        .user-info { background: #e9ecef; padding: 8px; margin: 5px 0; border-radius: 4px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 自动评论功能测试</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 测试1: 检查配置
    echo "<div class='test-section'>";
    echo "<h2>测试 1: 检查配置</h2>";
    
    $config = get_auto_comment_config();
    echo "<div class='info'>";
    echo "功能状态: " . ($config['enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
    echo "评论数量: {$config['comment_count']} 条<br>";
    echo "延迟发布: " . ($config['delay_enabled'] ? '✅ 已启用' : '❌ 已禁用') . "<br>";
    echo "延迟范围: {$config['min_delay']}-{$config['max_delay']} 分钟<br>";
    echo "</div>";
    echo "</div>";
    
    // 测试2: 检查评论模板
    echo "<div class='test-section'>";
    echo "<h2>测试 2: 检查评论模板</h2>";
    
    $templates = get_random_comment_contents(5);
    echo "<div class='success'>✅ 成功获取 " . count($templates) . " 条评论模板</div>";
    
    echo "<h3>随机评论内容预览:</h3>";
    foreach ($templates as $i => $content) {
        echo "<div class='comment-preview'>" . ($i + 1) . ". " . htmlspecialchars($content) . "</div>";
    }
    echo "</div>";
    
    // 测试3: 检查用户选择
    echo "<div class='test-section'>";
    echo "<h2>测试 3: 检查用户选择</h2>";
    
    $users = get_random_comment_users(5);
    echo "<div class='success'>✅ 成功获取 " . count($users) . " 个评论用户</div>";
    
    echo "<h3>随机用户预览:</h3>";
    foreach ($users as $i => $user) {
        $type = $user['is_anonymous'] ? '匿名用户' : '注册会员';
        echo "<div class='user-info'>";
        echo ($i + 1) . ". {$user['user_name']} ({$type})";
        if (!$user['is_anonymous']) {
            echo " - ID: {$user['user_id']}";
        }
        echo "</div>";
    }
    echo "</div>";
    
    // 测试4: 检查评分生成
    echo "<div class='test-section'>";
    echo "<h2>测试 4: 检查评分生成</h2>";
    
    echo "<h3>随机评分预览:</h3>";
    for ($i = 1; $i <= 5; $i++) {
        $ratings = generate_random_ratings();
        echo "<div class='user-info'>";
        echo "评分 {$i}: 内容质量 {$ratings['content_quality']}★ | ";
        echo "服务质量 {$ratings['service_quality']}★ | ";
        echo "诚信度 {$ratings['trust_level']}★";
        echo "</div>";
    }
    echo "</div>";
    
    // 测试5: 获取可测试的网站
    echo "<div class='test-section'>";
    echo "<h2>测试 5: 获取可测试的网站</h2>";
    
    $website_table = $DB->table('websites');
    $query = $DB->query("SELECT web_id, web_name, web_url, web_status FROM $website_table WHERE web_status = 3 ORDER BY web_id DESC LIMIT 5");
    
    $test_websites = array();
    while ($row = $DB->fetch_array($query)) {
        $test_websites[] = $row;
    }
    
    if (empty($test_websites)) {
        echo "<div class='error'>❌ 没有找到已发布的网站进行测试</div>";
        echo "<div class='info'>请先在后台审核通过一些网站，或者手动将网站状态设为3（已审核）</div>";
    } else {
        echo "<div class='success'>✅ 找到 " . count($test_websites) . " 个可测试的网站</div>";
        
        echo "<h3>可测试的网站:</h3>";
        foreach ($test_websites as $website) {
            echo "<div class='user-info'>";
            echo "ID: {$website['web_id']} - {$website['web_name']} ({$website['web_url']})";
            echo " <a href='?action=test_add&web_id={$website['web_id']}' class='btn' style='padding: 5px 10px; font-size: 12px;'>测试添加评论</a>";
            echo "</div>";
        }
    }
    echo "</div>";
    
    // 处理测试添加评论的请求
    if (isset($_GET['action']) && $_GET['action'] == 'test_add' && isset($_GET['web_id'])) {
        echo "<div class='test-section'>";
        echo "<h2>测试 6: 实际添加评论</h2>";
        
        $test_web_id = intval($_GET['web_id']);
        $result = auto_add_website_comments($test_web_id, 3); // 测试添加3条评论
        
        if ($result['success']) {
            echo "<div class='success'>✅ " . $result['message'] . "</div>";
            
            // 显示添加的评论
            $comment_table = $DB->table('website_comments');
            $query = $DB->query("SELECT * FROM $comment_table WHERE web_id = $test_web_id ORDER BY create_time DESC LIMIT 5");
            
            echo "<h3>最新添加的评论:</h3>";
            while ($comment = $DB->fetch_array($query)) {
                echo "<div class='comment-preview'>";
                echo "<strong>{$comment['user_name']}</strong> ";
                echo "({$comment['content_quality']}★ {$comment['service_quality']}★ {$comment['trust_level']}★)<br>";
                echo htmlspecialchars($comment['comment_content']);
                echo "<br><small>时间: " . date('Y-m-d H:i:s', $comment['create_time']) . "</small>";
                echo "</div>";
            }
        } else {
            echo "<div class='error'>❌ " . $result['message'] . "</div>";
        }
        echo "</div>";
    }
    
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='install_auto_comments.php' class='btn'>重新安装</a>";
    echo "<a href='system/option.php' class='btn'>后台配置</a>";
    echo "<a href='index.php' class='btn'>返回首页</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 测试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
