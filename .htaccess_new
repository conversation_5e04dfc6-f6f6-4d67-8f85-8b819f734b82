RewriteEngine On

# 排除实际存在的文件和目录
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# 处理查询参数中的mod参数（解决相对路径问题）
RewriteCond %{QUERY_STRING} ^mod=(.+)$
RewriteRule ^.*$ index.php?mod=%1 [L]

RewriteRule ^(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(/?)$ index.php?mod=$1 [L]

# 新增模块基础路由
RewriteRule ^(ajaxget|getdata|api|pending|addurl|quicksubmit|blacklist|rejected|vip_detail|vip_list|datastats)(/?)$ index.php?mod=$1 [L]

RewriteRule ^update/([0-9]+)\.html$ index.php?mod=update&days=$1 [L]
RewriteRule ^update/([0-9]+)-([0-9]+)\.html$ index.php?mod=update&days=$1&page=$2 [L]
RewriteRule ^archives/([0-9]+)\.html$ index.php?mod=archives&date=$1 [L]
RewriteRule ^archives/([0-9]+)-([0-9]+)\.html$ index.php?mod=archives&date=$1&page=$2 [L]
RewriteRule ^search/(name|url|tags|intro|br|pr|art)/(.+)-([0-9]+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L]
RewriteRule ^(br|pr)/(.+)-([0-9]+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^(br|pr)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L]
RewriteRule ^view/([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo/([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo-([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo-([0-9]+)-(.+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo/([0-9]+)/(.+)/(.+)/?$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo/([0-9]+)/(.+)/?$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo/([0-9]+)/?$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^site/([0-9]+)-(.+)(/?)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^artinfo/([0-9]+)\.html$ index.php?mod=artinfo&aid=$1 [L]
RewriteRule ^article/([0-9]+)-(.+)/?$ index.php?mod=artinfo&aid=$1 [L]
RewriteRule ^article/([0-9]+)/?$ index.php?mod=artinfo&aid=$1 [L]
RewriteRule ^linkinfo/([0-9]+)\.html$ index.php?mod=linkinfo&lid=$1 [L]
RewriteRule ^diypage/([0-9]+)\.html$ index.php?mod=diypage&pid=$1 [L]
RewriteRule ^rssfeed/([0-9]+)\.html$ index.php?mod=rssfeed&cid=$1 [L]
RewriteRule ^sitemap/([0-9]+)\.html$ index.php?mod=sitemap&cid=$1 [L]
RewriteRule ^webdir/(.+)/([0-9]+)\.html$ index.php?mod=webdir&cid=$2 [L]
RewriteRule ^webdir/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=webdir&cid=$2&page=$3 [L]
RewriteRule ^webdir/(.+)/([0-9]+)-(.+)-([0-9]+)\.html$ index.php?mod=webdir&cid=$2&sort=$3&page=$4 [L]
RewriteRule ^weblink/(.+)/([0-9]+)\.html$ index.php?mod=weblink&cid=$2 [L]
RewriteRule ^weblink/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=weblink&cid=$2&page=$3 [L]
RewriteRule ^weblink/(.+)/([0-9]+)-(.+)-([0-9]+)\.html$ index.php?mod=weblink&cid=$2&sort=$3&page=$4 [L]
RewriteRule ^article/(.+)/([0-9]+)\.html$ index.php?mod=article&cid=$2 [L]
RewriteRule ^article/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=article&cid=$2&page=$3 [L]
RewriteRule ^rssfeed/(.+)/([0-9]+)\.html$ index.php?mod=rssfeed&cid=$2 [L]
RewriteRule ^rssfeed/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=rssfeed&cid=$2&page=$3 [L]

# 新增模块详细页面路由
RewriteRule ^pending_detail/([0-9]+)\.html$ index.php?mod=pending_detail&id=$1 [L]
RewriteRule ^blacklist_detail/([0-9]+)\.html$ index.php?mod=blacklist_detail&id=$1 [L]
RewriteRule ^rejected_detail/([0-9]+)\.html$ index.php?mod=rejected_detail&id=$1 [L]
RewriteRule ^vip_detail/([0-9]+)\.html$ index.php?mod=vip_detail&id=$1 [L]

# 新增模块列表页面路由
RewriteRule ^pending/([0-9]+)\.html$ index.php?mod=pending&page=$1 [L]
RewriteRule ^blacklist/([0-9]+)\.html$ index.php?mod=blacklist&page=$1 [L]
RewriteRule ^rejected/([0-9]+)\.html$ index.php?mod=rejected&page=$1 [L]
RewriteRule ^vip_list/([0-9]+)\.html$ index.php?mod=vip_list&page=$1 [L]

# 数据统计和API路由
RewriteRule ^datastats/(.+)\.html$ index.php?mod=datastats&type=$1 [L]
RewriteRule ^api/(.+)\.html$ index.php?mod=api&type=$1 [L]
RewriteRule ^ajaxget/(.+)\.html$ index.php?mod=ajaxget&type=$1 [L]
RewriteRule ^getdata/(.+)/([0-9]+)\.html$ index.php?mod=getdata&type=$1&wid=$2 [L]
