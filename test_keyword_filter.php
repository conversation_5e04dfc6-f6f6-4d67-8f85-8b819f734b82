<?php
/**
 * 关键词过滤功能测试脚本
 */

echo "<h1>关键词过滤功能测试</h1>";

try {
    // 引入初始化文件
    require_once('./source/init.php');
    require_once('./module/website_comments.php');
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
    echo "✅ 初始化文件和评论模块加载成功";
    echo "</div>";
    
    // 检查censor_words函数是否存在
    echo "<h2>函数可用性检查</h2>";
    if (function_exists('censor_words')) {
        echo "<div style='color: green; margin: 5px 0;'>✅ censor_words() 函数存在</div>";
    } else {
        echo "<div style='color: red; margin: 5px 0;'>❌ censor_words() 函数不存在</div>";
    }
    
    // 检查配置项
    echo "<h2>配置项检查</h2>";
    if (isset($options['filter_words']) && !empty($options['filter_words'])) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
        echo "✅ 非法关键词配置存在";
        echo "</div>";
        
        echo "<h3>当前配置的非法关键词：</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<strong>关键词列表：</strong><br>";
        echo htmlspecialchars($options['filter_words']);
        echo "</div>";
        
        // 解析关键词
        $keywords = explode(',', $options['filter_words']);
        $keywords = array_map('trim', $keywords);
        $keywords = array_filter($keywords);
        
        echo "<p><strong>解析后的关键词数量：</strong> " . count($keywords) . " 个</p>";
        
    } else {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; border-radius: 4px; margin: 10px 0;'>";
        echo "⚠️ 非法关键词配置为空或不存在";
        echo "</div>";
        echo "<p>请到后台 系统设置 → 基本设置 中配置非法关键词</p>";
    }
    
    // 测试关键词过滤功能
    echo "<h2>关键词过滤测试</h2>";
    
    if (isset($options['filter_words']) && !empty($options['filter_words'])) {
        $test_contents = array(
            "这是一个正常的评论内容，没有任何问题。",
            "这个网站很好用，推荐大家使用。",
            "网站速度很快，内容丰富。",
            "这里有色情内容", // 假设色情是关键词
            "赌博网站推荐", // 假设赌博是关键词
            "毒品交易信息", // 假设毒品是关键词
            "正常评论中包含sb这个词", // 假设sb是关键词
        );
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>测试内容</th><th>检查结果</th><th>状态</th></tr>";
        
        foreach ($test_contents as $content) {
            $is_clean = censor_words($options['filter_words'], $content);
            $status = $is_clean ? "通过" : "被拦截";
            $color = $is_clean ? "green" : "red";
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($content) . "</td>";
            echo "<td style='color: $color;'>" . ($is_clean ? "✅ 内容正常" : "❌ 包含非法关键词") . "</td>";
            echo "<td style='color: $color;'><strong>$status</strong></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // 测试评论提交功能
        echo "<h2>评论提交测试</h2>";
        
        // 模拟评论数据
        $test_comment_data = array(
            'web_id' => 1,
            'comment_content' => '这是一个包含色情内容的测试评论', // 假设会被过滤
            'content_quality' => 5,
            'service_quality' => 5,
            'trust_level' => 5,
            'user_id' => 0,
            'user_email' => '',
            'user_name' => '匿名'
        );
        
        echo "<h3>测试评论数据：</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<strong>评论内容：</strong> " . htmlspecialchars($test_comment_data['comment_content']) . "<br>";
        echo "<strong>网站ID：</strong> " . $test_comment_data['web_id'] . "<br>";
        echo "<strong>用户类型：</strong> 匿名用户";
        echo "</div>";
        
        // 不实际提交到数据库，只测试过滤逻辑
        echo "<h3>过滤测试结果：</h3>";
        $filter_result = censor_words($options['filter_words'], $test_comment_data['comment_content']);
        
        if ($filter_result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ 评论内容通过关键词检查，可以提交";
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
            echo "❌ 评论内容包含非法关键词，将被拒绝";
            echo "</div>";
        }
        
    } else {
        echo "<p>无法进行过滤测试，因为没有配置关键词。</p>";
    }
    
    // 显示域名过滤测试
    echo "<h2>域名过滤测试</h2>";
    
    $domain_test_contents = array(
        "这是正常内容",
        "访问 www.example.com 了解更多",
        "我的网站是 test.cn",
        "联系邮箱：<EMAIL>",
        "推荐网站：https://www.google.com"
    );
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>原始内容</th><th>过滤后内容</th><th>状态</th></tr>";
    
    foreach ($domain_test_contents as $content) {
        $filtered = filter_domain_names($content);
        $is_filtered = ($filtered !== $content);
        $status = $is_filtered ? "已过滤" : "无需过滤";
        $color = $is_filtered ? "orange" : "green";
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($content) . "</td>";
        echo "<td>" . htmlspecialchars($filtered) . "</td>";
        echo "<td style='color: $color;'><strong>$status</strong></td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
    echo "❌ 错误：" . $e->getMessage();
    echo "</div>";
}

echo "<h2>配置说明</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>如何配置非法关键词：</h3>";
echo "<ol>";
echo "<li>登录后台管理系统</li>";
echo "<li>进入 系统设置 → 基本设置</li>";
echo "<li>找到 '非法关键词过滤设置' 选项</li>";
echo "<li>在文本框中输入关键词，用英文逗号分隔</li>";
echo "<li>例如：色情,赌博,毒品,诈骗,sb,垃圾</li>";
echo "<li>保存设置</li>";
echo "</ol>";
echo "</div>";

echo "<h2>测试完成</h2>";
echo "<p>现在评论功能已经集成了后台的非法关键词过滤系统。</p>";
echo "<p><a href='?mod=siteinfo&wid=1'>点击这里测试评论功能</a>（如果网站ID 1存在）</p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
