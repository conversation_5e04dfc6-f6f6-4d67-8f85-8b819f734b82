<?php
/**
 * 专门调试搜索推送模块的问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>搜索推送模块调试</h2>";

// 1. 基本环境设置
define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "<h3>1. 基本环境</h3>";
echo "ROOT_PATH: " . ROOT_PATH . "<br/>";
echo "APP_PATH: " . APP_PATH . "<br/>";

// 2. 加载基础文件
echo "<h3>2. 加载基础文件</h3>";
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');
require('./system/function.php');
echo "✓ 基础文件加载完成<br/>";

// 3. 加载配置
echo "<h3>3. 加载配置</h3>";
if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
require(ROOT_PATH.'data/static/options.php');
$options = $static_data;
echo "✓ 配置加载完成<br/>";

// 4. 检查搜索推送模块文件
echo "<h3>4. 检查搜索推送模块文件</h3>";
$module_file = APP_PATH.'module/search_push.php';
echo "模块文件路径: " . $module_file . "<br/>";

if (file_exists($module_file)) {
    echo "✓ 模块文件存在<br/>";
    echo "文件大小: " . filesize($module_file) . " 字节<br/>";
    
    // 检查文件内容
    $content = file_get_contents($module_file);
    if (strpos($content, 'function get_push_config') !== false) {
        echo "✓ get_push_config 函数定义存在<br/>";
    } else {
        echo "✗ get_push_config 函数定义不存在<br/>";
    }
    
    if (strpos($content, 'function get_all_urls') !== false) {
        echo "✓ get_all_urls 函数定义存在<br/>";
    } else {
        echo "✗ get_all_urls 函数定义不存在<br/>";
    }
} else {
    echo "✗ 模块文件不存在<br/>";
    exit;
}

// 5. 逐步加载模块文件
echo "<h3>5. 逐步加载模块文件</h3>";

// 先检查语法
echo "检查PHP语法...<br/>";
$syntax_check = shell_exec("php -l " . escapeshellarg($module_file) . " 2>&1");
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "✓ PHP语法检查通过<br/>";
} else {
    echo "✗ PHP语法错误: " . htmlspecialchars($syntax_check) . "<br/>";
    exit;
}

// 尝试包含文件
echo "尝试包含模块文件...<br/>";
try {
    ob_start();
    include($module_file);
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "⚠ 模块文件产生了输出: " . htmlspecialchars($output) . "<br/>";
    } else {
        echo "✓ 模块文件包含成功，无输出<br/>";
    }
} catch (ParseError $e) {
    echo "✗ 解析错误: " . $e->getMessage() . "<br/>";
    echo "错误位置: 第 " . $e->getLine() . " 行<br/>";
    exit;
} catch (Error $e) {
    echo "✗ 致命错误: " . $e->getMessage() . "<br/>";
    echo "错误位置: 第 " . $e->getLine() . " 行<br/>";
    exit;
} catch (Exception $e) {
    echo "✗ 异常: " . $e->getMessage() . "<br/>";
    exit;
}

// 6. 检查函数是否可用
echo "<h3>6. 检查函数可用性</h3>";
$functions = array(
    'get_push_config',
    'save_push_config', 
    'get_all_urls',
    'push_to_baidu',
    'push_to_google',
    'push_to_bing',
    'generate_sitemap_xml',
    'log_push_result',
    'get_push_logs'
);

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✓ 函数 {$func} 存在<br/>";
    } else {
        echo "✗ 函数 {$func} 不存在<br/>";
    }
}

// 7. 测试关键函数
echo "<h3>7. 测试关键函数</h3>";

if (function_exists('get_push_config')) {
    try {
        $config = get_push_config();
        echo "✓ get_push_config 执行成功<br/>";
        echo "配置数据类型: " . gettype($config) . "<br/>";
        if (is_array($config)) {
            echo "配置项数量: " . count($config) . "<br/>";
        }
    } catch (Exception $e) {
        echo "✗ get_push_config 执行失败: " . $e->getMessage() . "<br/>";
    }
} else {
    echo "⚠ get_push_config 函数不存在，跳过测试<br/>";
}

if (function_exists('get_all_urls')) {
    try {
        $urls = get_all_urls();
        echo "✓ get_all_urls 执行成功<br/>";
        echo "URL数量: " . count($urls) . "<br/>";
        if (count($urls) > 0) {
            echo "第一个URL: " . htmlspecialchars($urls[0]) . "<br/>";
        }
    } catch (Exception $e) {
        echo "✗ get_all_urls 执行失败: " . $e->getMessage() . "<br/>";
    }
} else {
    echo "⚠ get_all_urls 函数不存在，跳过测试<br/>";
}

// 8. 检查数据库表
echo "<h3>8. 检查数据库表</h3>";
$tables = array('options', 'categories', 'websites', 'articles');

foreach ($tables as $table_name) {
    try {
        $table = $DB->table($table_name);
        $result = $DB->get_one("SELECT COUNT(*) as count FROM $table");
        echo "✓ 表 {$table_name} 存在，记录数: " . $result['count'] . "<br/>";
    } catch (Exception $e) {
        echo "✗ 表 {$table_name} 检查失败: " . $e->getMessage() . "<br/>";
    }
}

// 9. 模拟后台页面处理
echo "<h3>9. 模拟后台页面处理</h3>";

// 创建虚拟登录用户
$myself = array(
    'user_id' => 1,
    'user_email' => '<EMAIL>',
    'login_time' => date('Y-m-d H:i:s'),
    'login_ip' => '127.0.0.1',
    'login_count' => 1,
);
$smarty->assign('myself', $myself);

// 设置页面变量
$action = 'config';
$fileurl = 'search_push.php';
$pagetitle = '搜索引擎推送配置';
$tempfile = 'search_push.html';

if (function_exists('get_push_config')) {
    try {
        $config = get_push_config();
        
        $smarty->assign('action', $action);
        $smarty->assign('fileurl', $fileurl);
        $smarty->assign('pagetitle', $pagetitle);
        $smarty->assign('config', $config);
        
        echo "✓ Smarty变量分配成功<br/>";
        echo "Action: " . $action . "<br/>";
        echo "Page Title: " . $pagetitle . "<br/>";
        
    } catch (Exception $e) {
        echo "✗ 页面处理失败: " . $e->getMessage() . "<br/>";
    }
}

// 10. 检查模板文件
echo "<h3>10. 检查模板文件</h3>";
$template_file = ROOT_PATH . 'themes/system/search_push.html';

if (file_exists($template_file)) {
    echo "✓ 模板文件存在<br/>";
    
    try {
        if (function_exists('template_exists')) {
            template_exists('search_push.html');
            echo "✓ 模板文件验证通过<br/>";
        }
        
        // 尝试渲染模板
        echo "尝试渲染模板...<br/>";
        ob_start();
        $smarty->display('search_push.html');
        $template_output = ob_get_clean();
        
        if (!empty($template_output)) {
            echo "✓ 模板渲染成功，输出长度: " . strlen($template_output) . " 字符<br/>";
            echo "输出前100字符: " . htmlspecialchars(substr($template_output, 0, 100)) . "...<br/>";
        } else {
            echo "⚠ 模板渲染成功但无输出<br/>";
        }
        
    } catch (Exception $e) {
        echo "✗ 模板处理失败: " . $e->getMessage() . "<br/>";
        echo "错误文件: " . $e->getFile() . "<br/>";
        echo "错误行号: " . $e->getLine() . "<br/>";
    }
} else {
    echo "✗ 模板文件不存在<br/>";
}

echo "<h3>调试完成</h3>";
echo "<p>如果所有检查都通过，问题可能在于：</p>";
echo "<ul>";
echo "<li>用户权限验证失败</li>";
echo "<li>模板缓存问题</li>";
echo "<li>浏览器缓存问题</li>";
echo "<li>服务器配置问题</li>";
echo "</ul>";
?>
