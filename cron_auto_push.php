<?php
/**
 * 自动推送定时任务
 * 用于定时推送网站URL到各大搜索引擎
 * 
 * 使用方法：
 * 1. 在后台配置好各搜索引擎的API密钥
 * 2. 启用自动推送功能
 * 3. 将此文件添加到服务器的crontab中
 * 
 * crontab示例：
 * 0 */24 * * * /usr/bin/php /path/to/your/website/cron_auto_push.php
 */

// 设置执行时间限制
set_time_limit(300); // 5分钟

// 引入必要文件
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

require_once(APP_PATH.'init.php');
require_once(APP_PATH.'module/search_push.php');

// 加载网站配置
define('IN_HANFOX', true); // 兼容配置文件的安全检查
require(ROOT_PATH.'data/static/options.php');
$options = $static_data;

// 检查是否启用自动推送
$table = $DB->table('options');
$auto_push_enabled = $DB->get_one("SELECT option_value FROM $table WHERE option_name='auto_push_enabled'");

if (!$auto_push_enabled || $auto_push_enabled['option_value'] != 'yes') {
    echo "自动推送功能未启用\n";
    exit;
}

// 获取自动推送配置
$auto_push_engines = $DB->get_one("SELECT option_value FROM $table WHERE option_name='auto_push_engines'");
$auto_push_interval = $DB->get_one("SELECT option_value FROM $table WHERE option_name='auto_push_interval'");

$engines = $auto_push_engines ? explode(',', $auto_push_engines['option_value']) : array();
$interval = $auto_push_interval ? intval($auto_push_interval['option_value']) : 24;

if (empty($engines)) {
    echo "未配置推送搜索引擎\n";
    exit;
}

// 检查上次推送时间
$last_push_time = $DB->get_one("SELECT option_value FROM $table WHERE option_name='last_auto_push_time'");
$last_time = $last_push_time ? intval($last_push_time['option_value']) : 0;
$current_time = time();

// 检查是否到了推送时间
if ($current_time - $last_time < $interval * 3600) {
    echo "还未到推送时间，上次推送：" . date('Y-m-d H:i:s', $last_time) . "\n";
    exit;
}

echo "开始自动推送...\n";
echo "推送时间：" . date('Y-m-d H:i:s') . "\n";

// 获取推送配置
$config = get_push_config();

// 获取URL列表（限制数量避免超时）
$urls = get_all_urls();
$urls = array_slice($urls, 0, 100); // 限制100个URL

echo "准备推送 " . count($urls) . " 个URL\n";

// 推送到各个搜索引擎
foreach ($engines as $engine) {
    echo "推送到 {$engine}...\n";
    
    switch ($engine) {
        case 'baidu':
            if (!empty($config['baidu_token'])) {
                $result = push_to_baidu($urls, $config['baidu_token']);
                log_push_result('baidu', $result, count($urls));
                echo "百度推送结果：" . $result['message'] . "\n";
            } else {
                echo "百度推送Token未配置\n";
            }
            break;
            
        case 'google':
            if (!empty($config['google_key'])) {
                // 先生成sitemap
                generate_sitemap_xml();
                $result = push_to_google($options['site_url'], $config['google_key']);
                log_push_result('google', $result, count($urls));
                echo "谷歌推送结果：" . $result['message'] . "\n";
            } else {
                echo "谷歌API密钥未配置\n";
            }
            break;
            
        case 'bing':
            if (!empty($config['bing_key'])) {
                $result = push_to_bing($urls, $config['bing_key']);
                log_push_result('bing', $result, count($urls));
                echo "必应推送结果：" . $result['message'] . "\n";
            } else {
                echo "必应API密钥未配置\n";
            }
            break;
    }
    
    // 每个搜索引擎之间间隔2秒
    sleep(2);
}

// 更新最后推送时间
$DB->query("REPLACE INTO $table (option_name, option_value) VALUES ('last_auto_push_time', '$current_time')");

echo "自动推送完成\n";
echo "下次推送时间：" . date('Y-m-d H:i:s', $current_time + $interval * 3600) . "\n";
?>
