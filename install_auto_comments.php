<?php
/**
 * 自动评论功能安装脚本
 * 创建必要的数据库表和初始化配置
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>自动评论功能安装</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🚀 自动评论功能安装程序</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败，请检查配置");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 1. 创建自动评论模板表
    echo "<div class='step'>";
    echo "<h2>步骤 1: 创建自动评论模板表</h2>";
    
    $table_name = $DB->table('auto_comment_templates');
    
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS `$table_name` (
      `template_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
      `content` text NOT NULL COMMENT '评论内容',
      `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '评论分类',
      `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=启用，0=禁用',
      `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
      `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
      PRIMARY KEY (`template_id`),
      KEY `status` (`status`),
      KEY `category` (`category`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;
    ";
    
    $DB->query($create_table_sql);
    echo "<div class='success'>✅ 自动评论模板表创建成功</div>";
    echo "</div>";
    
    // 2. 插入默认评论模板
    echo "<div class='step'>";
    echo "<h2>步骤 2: 插入默认评论模板</h2>";
    
    // 检查是否已有数据
    $existing_count = $DB->get_count($table_name, '1');
    if ($existing_count > 0) {
        echo "<div class='info'>ℹ️ 检测到已有 {$existing_count} 条评论模板，跳过插入</div>";
    } else {
        $default_comments = array(
            '网站设计简洁大方，内容丰富且更新及时，用户体验非常棒，强烈推荐！',
            '界面美观，功能强大，信息准确，是我日常学习和工作的得力助手。',
            '网站加载速度快，资源全面，使用起来非常顺畅，点赞！',
            '内容专业性强，分类清晰，完全满足了我的需求，很赞！',
            '设计贴心，操作便捷，信息准确无误，真是一个优质的网站！',
            '资源丰富，内容实用，是我工作中不可或缺的工具！',
            '网站布局合理，功能齐全，完全超出我的预期，太好了！',
            '信息全面，更新及时，帮助我快速解决了问题，非常实用！',
            '用户界面友好，操作简单，完全适合新手使用！',
            '网站内容质量高，分类明确，查找信息非常方便！',
            '资源更新速度快，专业性强，是我学习的首选网站！',
            '界面清爽，设计感十足，使用体验非常舒适！',
            '功能多样且实用，内容涵盖范围广，太棒了！',
            '网站加载速度很快，资源丰富，完全满足我的需求！',
            '内容详细且有深度，分类清晰，非常适合研究使用！',
            '设计简约而不失美感，功能强大，用户体验极佳！',
            '网站信息准确，更新及时，帮助我节省了很多时间！',
            '界面美观，操作流畅，完全是一个高质量的网站！',
            '资源丰富，功能齐全，是我工作中常用的工具之一！',
            '网站内容专业且全面，设计贴心，非常值得推荐！',
            '页面响应速度快，内容组织有序，使用起来很舒心！',
            '功能实用，界面简洁，完全符合我的使用习惯！',
            '内容权威可靠，更新频率高，是个值得信赖的网站！',
            '设计现代化，功能完善，给我留下了深刻的印象！',
            '网站服务周到，内容丰富，真正做到了用户至上！'
        );
        
        $current_time = time();
        $inserted_count = 0;
        
        foreach ($default_comments as $content) {
            $data = array(
                'content' => $content,
                'category' => 'general',
                'status' => 1,
                'create_time' => $current_time,
                'update_time' => $current_time
            );
            
            if ($DB->insert($table_name, $data)) {
                $inserted_count++;
            }
        }
        
        echo "<div class='success'>✅ 成功插入 {$inserted_count} 条默认评论模板</div>";
    }
    echo "</div>";
    
    // 3. 添加配置选项
    echo "<div class='step'>";
    echo "<h2>步骤 3: 添加配置选项</h2>";
    
    $options_table = $DB->table('options');
    $config_options = array(
        'auto_comment_enabled' => 'yes',
        'auto_comment_count' => '5',
        'auto_comment_delay' => 'yes',
        'auto_comment_min_delay' => '1',
        'auto_comment_max_delay' => '30'
    );
    
    foreach ($config_options as $option_name => $option_value) {
        // 检查选项是否已存在
        $existing = $DB->fetch_one("SELECT option_name FROM $options_table WHERE option_name = '$option_name'");
        
        if ($existing) {
            // 更新现有选项
            $DB->update($options_table, array('option_value' => $option_value), array('option_name' => $option_name));
            echo "<div class='info'>ℹ️ 更新配置选项: {$option_name} = {$option_value}</div>";
        } else {
            // 插入新选项
            $DB->insert($options_table, array('option_name' => $option_name, 'option_value' => $option_value));
            echo "<div class='success'>✅ 添加配置选项: {$option_name} = {$option_value}</div>";
        }
    }
    echo "</div>";
    
    // 4. 验证安装
    echo "<div class='step'>";
    echo "<h2>步骤 4: 验证安装</h2>";
    
    $template_count = $DB->get_count($table_name, 'status = 1');
    echo "<div class='success'>✅ 评论模板表: {$template_count} 条可用模板</div>";
    
    $config_count = $DB->get_count($options_table, "option_name LIKE 'auto_comment_%'");
    echo "<div class='success'>✅ 配置选项: {$config_count} 个相关配置</div>";
    
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 安装完成！</h2>";
    echo "<p>自动评论功能已成功安装并配置。</p>";
    echo "<p><strong>功能说明：</strong></p>";
    echo "<ul>";
    echo "<li>当网站从待审核状态变为已审核时，系统会自动添加5条随机评论</li>";
    echo "<li>评论内容从25条预设模板中随机选择</li>";
    echo "<li>评论者优先使用现有会员，不足时使用匿名用户</li>";
    echo "<li>评分为4-5星的高质量评分</li>";
    echo "<li>评论时间会有1-30分钟的随机间隔</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin-top: 30px;'>";
    echo "<a href='system/option.php' class='btn'>前往后台配置</a>";
    echo "<a href='test_auto_comments.php' class='btn'>测试功能</a>";
    echo "<a href='index.php' class='btn'>返回首页</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 安装失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库配置和权限设置。</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
