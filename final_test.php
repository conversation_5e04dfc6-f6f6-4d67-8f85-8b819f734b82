<?php
/**
 * 最终功能测试
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>搜索引擎推送功能最终测试</h2>";

// 1. 基本环境检查
echo "<h3>1. 基本环境检查</h3>";

define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "✓ 常量定义完成<br/>";

// 2. 加载系统文件
echo "<h3>2. 加载系统文件</h3>";

try {
    require(APP_PATH.'init.php');
    echo "✓ init.php 加载成功<br/>";
    
    require(APP_PATH.'module/static.php');
    echo "✓ static.php 加载成功<br/>";
    
    require('./system/function.php');
    echo "✓ function.php 加载成功<br/>";
    
} catch (Exception $e) {
    echo "✗ 系统文件加载失败: " . $e->getMessage() . "<br/>";
    exit;
}

// 3. 加载配置
echo "<h3>3. 加载配置</h3>";

try {
    if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
    require(ROOT_PATH.'data/static/options.php');
    $options = $static_data;
    echo "✓ 配置加载成功<br/>";
    echo "网站URL: " . $options['site_url'] . "<br/>";
} catch (Exception $e) {
    echo "✗ 配置加载失败: " . $e->getMessage() . "<br/>";
    exit;
}

// 4. 加载搜索推送模块
echo "<h3>4. 加载搜索推送模块</h3>";

try {
    require(APP_PATH.'module/search_push.php');
    echo "✓ 搜索推送模块加载成功<br/>";
} catch (Exception $e) {
    echo "✗ 搜索推送模块加载失败: " . $e->getMessage() . "<br/>";
    exit;
}

// 5. 测试核心函数
echo "<h3>5. 测试核心函数</h3>";

$functions = array('get_push_config', 'save_push_config', 'get_all_urls', 'push_to_baidu', 'push_to_google', 'push_to_bing', 'generate_sitemap_xml', 'log_push_result', 'get_push_logs');

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✓ 函数 {$func} 存在<br/>";
    } else {
        echo "✗ 函数 {$func} 不存在<br/>";
    }
}

// 6. 测试配置功能
echo "<h3>6. 测试配置功能</h3>";

try {
    $config = get_push_config();
    echo "✓ 配置获取成功<br/>";
    echo "百度Token: " . (empty($config['baidu_token']) ? '未配置' : '已配置') . "<br/>";
    echo "谷歌API: " . (empty($config['google_key']) ? '未配置' : '已配置') . "<br/>";
    echo "必应API: " . (empty($config['bing_key']) ? '未配置' : '已配置') . "<br/>";
} catch (Exception $e) {
    echo "✗ 配置获取失败: " . $e->getMessage() . "<br/>";
}

// 7. 测试URL获取
echo "<h3>7. 测试URL获取</h3>";

try {
    $urls = get_all_urls();
    echo "✓ URL获取成功，共 " . count($urls) . " 个<br/>";
    if (count($urls) > 0) {
        echo "示例URL: " . htmlspecialchars($urls[0]) . "<br/>";
    }
} catch (Exception $e) {
    echo "✗ URL获取失败: " . $e->getMessage() . "<br/>";
}

// 8. 测试模板渲染
echo "<h3>8. 测试模板渲染</h3>";

try {
    // 创建虚拟用户
    $myself = array(
        'user_id' => 1,
        'user_email' => '<EMAIL>',
        'login_time' => date('Y-m-d H:i:s'),
        'login_ip' => '127.0.0.1',
        'login_count' => 1,
    );
    
    // 分配变量
    $smarty->assign('myself', $myself);
    $smarty->assign('action', 'config');
    $smarty->assign('fileurl', 'search_push.php');
    $smarty->assign('pagetitle', '搜索引擎推送配置');
    $smarty->assign('config', $config);
    
    echo "✓ Smarty变量分配成功<br/>";
    
    // 检查模板文件
    if (function_exists('template_exists')) {
        template_exists('search_push.html');
        echo "✓ 模板文件验证通过<br/>";
    }
    
    // 尝试渲染模板
    ob_start();
    $smarty->display('search_push.html');
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "✓ 模板渲染成功，输出长度: " . strlen($output) . " 字符<br/>";
    } else {
        echo "⚠ 模板渲染成功但无输出<br/>";
    }
    
} catch (Exception $e) {
    echo "✗ 模板渲染失败: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br/>";
}

// 9. 测试后台页面访问
echo "<h3>9. 后台页面访问测试</h3>";

echo "<p>如果上述所有测试都通过，您可以尝试访问以下链接：</p>";
echo "<ul>";
echo "<li><a href='system/search_push.php' target='_blank'>搜索引擎推送配置</a></li>";
echo "<li><a href='system/search_push.php?act=push' target='_blank'>手动推送</a></li>";
echo "<li><a href='system/search_push.php?act=logs' target='_blank'>推送日志</a></li>";
echo "</ul>";

// 10. 检查权限
echo "<h3>10. 权限检查</h3>";

if (isset($_COOKIE['user_auth'])) {
    echo "✓ 用户认证Cookie存在<br/>";
} else {
    echo "⚠ 用户认证Cookie不存在，需要先登录后台<br/>";
    echo "<a href='system/login.php' target='_blank'>点击这里登录后台</a><br/>";
}

echo "<h3>测试完成</h3>";
echo "<p><strong>如果所有测试都通过，搜索引擎推送功能应该可以正常使用了！</strong></p>";
?>
