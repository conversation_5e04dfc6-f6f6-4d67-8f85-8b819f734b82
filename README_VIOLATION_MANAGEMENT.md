# 评论违规检测和管理功能

## 功能概述

为95分类目录的评论系统添加了完整的违规内容检测和管理员删除功能，提升了内容管理的安全性和便利性。

## 主要功能

### ✅ 违规内容检测
- **自动检测**：使用系统配置的违规关键词自动检测评论内容
- **内容隐藏**：违规评论自动显示为"[此评论包含违规内容，已被隐藏]"
- **范围覆盖**：检测主评论和回复内容
- **实时处理**：在评论显示时进行检测，不影响数据库存储

### 🔧 管理员删除功能
- **权限控制**：只有管理员可以看到和使用删除功能
- **直接删除**：在评论区直接显示删除按钮
- **确认机制**：删除前弹出确认对话框
- **级联删除**：删除主评论时，其所有回复也会被删除
- **软删除**：将评论状态设为隐藏，数据仍保留在数据库中

### 📊 后台管理界面
- **评论列表**：完整的评论管理界面
- **搜索过滤**：支持按内容、网站、状态搜索
- **批量操作**：支持批量删除和恢复评论
- **违规检测**：一键检测所有评论的违规内容
- **统计信息**：显示总评论数、正常评论、已隐藏评论等统计

## 技术实现

### 1. 违规内容检测

**文件位置**：`module/website_comments.php`

```php
/**
 * 检测评论违规内容
 * @param string $content 评论内容
 * @return bool 是否违规
 */
function check_comment_violation($content) {
    global $options;
    
    // 使用系统的违规词检测
    if (!empty($options['filter_words'])) {
        if (!censor_words($options['filter_words'], $content)) {
            return true;
        }
    }
    
    return false;
}
```

### 2. 管理员权限检查

**文件位置**：`module/comment_handler.php`

```php
/**
 * 检查管理员权限
 */
function check_admin_permission() {
    // 检查管理员Cookie
    if (!isset($_COOKIE['user_auth'])) {
        return false;
    }
    
    // 解析和验证管理员认证信息
    // ...
    
    return true;
}
```

### 3. 前端集成

**文件位置**：`themes/default/siteinfo.html`

- 自动检查管理员权限
- 为管理员显示删除按钮
- 处理删除操作的AJAX请求
- 违规内容的隐藏显示

## 文件修改清单

### 新增文件
- `test_comment_violation.html` - 功能测试页面
- `system/comment_manage.php` - 后台评论管理
- `themes/system/comment_manage.html` - 管理界面模板
- `README_VIOLATION_MANAGEMENT.md` - 功能说明文档

### 修改文件
- `module/comment_handler.php` - 添加删除和权限检查接口
- `module/website_comments.php` - 添加违规检测功能
- `themes/default/siteinfo.html` - 前端界面和功能集成
- `themes/system/admin.html` - 后台菜单添加评论管理

## 使用说明

### 1. 违规词配置
1. 登录后台管理系统
2. 进入"系统设置" → "选项设置"
3. 在"非法关键词"字段中配置违规词，用逗号分隔
4. 保存设置

### 2. 前端使用
1. 普通用户访问网站详情页时，违规评论会自动隐藏
2. 管理员登录后访问网站详情页，可看到每条评论旁的删除按钮
3. 点击删除按钮可直接删除违规评论

### 3. 后台管理
1. 登录后台管理系统
2. 进入"用户管理" → "评论管理"
3. 可查看所有评论，搜索、过滤、批量操作
4. 使用"检测违规"功能一键检测所有评论

## 安全特性

### 权限控制
- 删除功能仅对管理员开放
- 基于Cookie的管理员身份验证
- 前端和后端双重权限检查

### 数据安全
- 软删除机制，数据不会真正丢失
- 违规检测不修改原始数据
- 操作日志和确认机制

### 性能优化
- 违规检测在显示时进行，不影响存储性能
- 使用现有的违规词检测函数，保持一致性
- 分页和搜索优化，支持大量评论数据

## 测试建议

### 1. 功能测试
1. 访问 `test_comment_violation.html` 查看功能说明
2. 配置违规关键词（如：测试,违规,敏感词）
3. 发表包含违规词的评论，验证隐藏效果
4. 使用管理员账号测试删除功能

### 2. 权限测试
1. 未登录状态：确认看不到删除按钮
2. 普通用户登录：确认看不到删除按钮
3. 管理员登录：确认可以看到和使用删除按钮

### 3. 后台测试
1. 访问后台评论管理界面
2. 测试搜索、过滤、批量操作功能
3. 测试违规检测功能

## 注意事项

1. **违规词配置**：确保在后台正确配置违规关键词
2. **管理员权限**：确保管理员已正确登录后台
3. **数据备份**：建议在大量删除操作前备份数据库
4. **性能监控**：大量评论时注意违规检测的性能影响
5. **定期清理**：建议定期检查和清理违规评论

## 后续扩展

### 可能的改进方向
1. **智能检测**：集成AI内容审核API
2. **用户举报**：添加用户举报违规内容功能
3. **自动处理**：违规内容自动隐藏或删除
4. **审核流程**：添加评论审核工作流
5. **统计分析**：违规内容趋势分析和报告

### 技术优化
1. **缓存机制**：违规词检测结果缓存
2. **异步处理**：大批量违规检测异步化
3. **日志记录**：详细的操作日志和审计
4. **API接口**：提供RESTful API接口

## 联系支持

如有问题或建议，请联系技术支持团队。
