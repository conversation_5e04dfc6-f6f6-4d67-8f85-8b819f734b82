<?php
/**
 * 不需要登录验证的测试页面
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>无权限验证测试</h2>";

// 设置基本常量
define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "<h3>1. 加载基础文件</h3>";

// 加载基础文件（跳过common.php避免权限检查）
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');
require('./system/function.php');

echo "✓ 基础文件加载完成<br/>";

echo "<h3>2. 加载配置</h3>";

if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
require(ROOT_PATH.'data/static/options.php');
$options = $static_data;

echo "✓ 配置加载完成<br/>";

echo "<h3>3. 加载搜索推送模块</h3>";

require(APP_PATH.'module/search_push.php');

echo "✓ 搜索推送模块加载完成<br/>";

echo "<h3>4. 测试核心功能</h3>";

// 测试配置获取
try {
    $config = get_push_config();
    echo "✓ 配置获取成功<br/>";
    echo "百度Token: " . (empty($config['baidu_token']) ? '未配置' : '已配置') . "<br/>";
    echo "谷歌API: " . (empty($config['google_key']) ? '未配置' : '已配置') . "<br/>";
    echo "必应API: " . (empty($config['bing_key']) ? '未配置' : '已配置') . "<br/>";
} catch (Exception $e) {
    echo "✗ 配置获取失败: " . $e->getMessage() . "<br/>";
}

// 测试URL获取
try {
    $urls = get_all_urls();
    echo "✓ URL获取成功，共 " . count($urls) . " 个<br/>";
} catch (Exception $e) {
    echo "✗ URL获取失败: " . $e->getMessage() . "<br/>";
}

echo "<h3>5. 模拟Smarty渲染</h3>";

// 创建虚拟用户
$myself = array(
    'user_id' => 1,
    'user_email' => '<EMAIL>',
    'login_time' => date('Y-m-d H:i:s'),
    'login_ip' => '127.0.0.1',
    'login_count' => 1,
);

// 分配变量
$smarty->assign('myself', $myself);
$smarty->assign('action', 'config');
$smarty->assign('fileurl', 'search_push.php');
$smarty->assign('pagetitle', '搜索引擎推送配置');
$smarty->assign('config', $config);

echo "✓ Smarty变量分配完成<br/>";

// 测试简化模板
echo "<h3>6. 测试简化模板</h3>";

try {
    echo "开始渲染简化模板...<br/>";
    echo "<hr/>";
    $smarty->display('search_push_simple.html');
    echo "<hr/>";
    echo "✓ 简化模板渲染成功<br/>";
} catch (Exception $e) {
    echo "✗ 简化模板渲染失败: " . $e->getMessage() . "<br/>";
}

// 测试完整模板
echo "<h3>7. 测试完整模板</h3>";

try {
    echo "开始渲染完整模板...<br/>";
    echo "<hr/>";
    $smarty->display('search_push.html');
    echo "<hr/>";
    echo "✓ 完整模板渲染成功<br/>";
} catch (Exception $e) {
    echo "✗ 完整模板渲染失败: " . $e->getMessage() . "<br/>";
    echo "错误详情: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br/>";
}

echo "<h3>测试完成</h3>";
?>
