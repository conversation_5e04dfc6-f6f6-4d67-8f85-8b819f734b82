RewriteEngine On

# 基础模块路由测试
RewriteRule ^article\.html$ index.php?mod=article [L]
RewriteRule ^article/?$ index.php?mod=article [L]

# 完整的基础模块路由
RewriteRule ^(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(\.html)?/?$ index.php?mod=$1 [L]

# 新增模块路由
RewriteRule ^(ajaxget|getdata|api|pending|addurl|quicksubmit|blacklist|rejected|vip_detail|vip_list|datastats)(\.html)?/?$ index.php?mod=$1 [L]

# 最近更新 - 按天数
RewriteRule ^update/([0-9]+)\.html$ index.php?mod=update&days=$1 [L]
RewriteRule ^update/([0-9]+)-([0-9]+)\.html$ index.php?mod=update&days=$1&page=$2 [L]

# 数据归档 - 按日期
RewriteRule ^archives/([0-9]+)\.html$ index.php?mod=archives&date=$1 [L]
RewriteRule ^archives/([0-9]+)-([0-9]+)\.html$ index.php?mod=archives&date=$1&page=$2 [L]

# 站内搜索 - 支持多种搜索类型
RewriteRule ^search/(name|url|tags|intro|br|pr|art)/(.+)-([0-9]+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L]

# BR/PR 搜索简化路由
RewriteRule ^(br|pr)/(.+)-([0-9]+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^(br|pr)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L]

# 站点详细信息 - 多种URL格式支持
RewriteRule ^view/([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo/([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^siteinfo-([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L]
RewriteRule ^site/([0-9]+)-(.+)/?\.html$ index.php?mod=siteinfo&wid=$1 [L]

# 文章详细信息
RewriteRule ^artinfo/([0-9]+)\.html$ index.php?mod=artinfo&aid=$1 [L]

# 链接详细信息
RewriteRule ^linkinfo/([0-9]+)\.html$ index.php?mod=linkinfo&lid=$1 [L]

# 自定义页面
RewriteRule ^diypage/([0-9]+)\.html$ index.php?mod=diypage&pid=$1 [L]

# 详情页面
RewriteRule ^pending_detail/([0-9]+)\.html$ index.php?mod=pending_detail&wid=$1 [L]
RewriteRule ^blacklist_detail/([0-9]+)\.html$ index.php?mod=blacklist_detail&wid=$1 [L]
RewriteRule ^rejected_detail/([0-9]+)\.html$ index.php?mod=rejected_detail&wid=$1 [L]
RewriteRule ^vip_detail/([0-9]+)\.html$ index.php?mod=vip_detail&wid=$1 [L]

# RSS订阅
RewriteRule ^rssfeed/([0-9]+)\.html$ index.php?mod=rssfeed&cid=$1 [L]
RewriteRule ^rssfeed/(.+)/([0-9]+)\.html$ index.php?mod=rssfeed&cid=$2 [L]
RewriteRule ^rssfeed/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=rssfeed&cid=$2&page=$3 [L]

# 网站地图
RewriteRule ^sitemap/([0-9]+)\.html$ index.php?mod=sitemap&cid=$1 [L]
RewriteRule ^sitemap/(.+)/([0-9]+)\.html$ index.php?mod=sitemap&cid=$2 [L]

# 分类目录
RewriteRule ^webdir/(.+)/([0-9]+)\.html$ index.php?mod=webdir&cid=$2 [L]
RewriteRule ^webdir/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=webdir&cid=$2&page=$3 [L]
RewriteRule ^webdir/(.+)/([0-9]+)-(.+)-([0-9]+)\.html$ index.php?mod=webdir&cid=$2&sort=$3&page=$4 [L]

# 友情链接
RewriteRule ^weblink/(.+)/([0-9]+)\.html$ index.php?mod=weblink&cid=$2 [L]
RewriteRule ^weblink/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=weblink&cid=$2&page=$3 [L]
RewriteRule ^weblink/(.+)/([0-9]+)-(.+)-([0-9]+)\.html$ index.php?mod=weblink&cid=$2&sort=$3&page=$4 [L]

# 文章分类
RewriteRule ^article/(.+)/([0-9]+)\.html$ index.php?mod=article&cid=$2 [L]
RewriteRule ^article/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=article&cid=$2&page=$3 [L]

# 列表页面
RewriteRule ^vip_list/([0-9]+)\.html$ index.php?mod=vip_list&page=$1 [L]
RewriteRule ^vip_list\.html$ index.php?mod=vip_list [L]
RewriteRule ^pending/([0-9]+)\.html$ index.php?mod=pending&page=$1 [L]
RewriteRule ^blacklist/([0-9]+)\.html$ index.php?mod=blacklist&page=$1 [L]
RewriteRule ^rejected/([0-9]+)\.html$ index.php?mod=rejected&page=$1 [L]

# 数据统计和API
RewriteRule ^datastats/(.+)\.html$ index.php?mod=datastats&type=$1 [L]
RewriteRule ^api/(.+)\.html$ index.php?mod=api&type=$1 [L]
RewriteRule ^ajaxget/(.+)\.html$ index.php?mod=ajaxget&type=$1 [L]
RewriteRule ^getdata/(.+)\.html$ index.php?mod=getdata&type=$1 [L]
