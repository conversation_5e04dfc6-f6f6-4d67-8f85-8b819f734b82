<?php
/*
 * 测试分页显示脚本
 * 用于验证各个页面的分页功能是否正常工作
 */

echo "<h1>分页显示测试报告</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.test-item { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { border-color: #4CAF50; background: #f1f8e9; }
.warning { border-color: #FF9800; background: #fff3e0; }
.error { border-color: #f44336; background: #ffebee; }
.code { background: #f5f5f5; padding: 10px; font-family: monospace; border-radius: 3px; margin: 10px 0; }
.test-links { margin: 10px 0; }
.test-links a { display: inline-block; margin: 5px 10px 5px 0; padding: 8px 15px; background: #2196F3; color: white; text-decoration: none; border-radius: 3px; }
.test-links a:hover { background: #1976D2; }
</style>\n";

// 需要测试的页面
$test_pages = [
    'webdir' => [
        'name' => '网站目录',
        'url' => '?mod=webdir',
        'test_urls' => [
            '?mod=webdir&page=1' => '第1页',
            '?mod=webdir&page=2' => '第2页',
            '?mod=webdir&cid=1&page=1' => '分类第1页',
            '?mod=webdir&cid=1&page=2' => '分类第2页'
        ]
    ],
    'article' => [
        'name' => '文章资讯',
        'url' => '?mod=article',
        'test_urls' => [
            '?mod=article&page=1' => '第1页',
            '?mod=article&page=2' => '第2页'
        ]
    ],
    'vip_list' => [
        'name' => 'VIP站点',
        'url' => '?mod=vip_list',
        'test_urls' => [
            '?mod=vip_list&page=1' => '第1页',
            '?mod=vip_list&page=2' => '第2页',
            '?mod=vip_list&cid=1&page=1' => '分类第1页'
        ]
    ],
    'blacklist' => [
        'name' => '黑名单',
        'url' => '?mod=blacklist',
        'test_urls' => [
            '?mod=blacklist&page=1' => '第1页',
            '?mod=blacklist&page=2' => '第2页',
            '?mod=blacklist&category=1&page=1' => '分类第1页'
        ]
    ],
    'pending' => [
        'name' => '待审核',
        'url' => '?mod=pending',
        'test_urls' => [
            '?mod=pending&page=1' => '第1页',
            '?mod=pending&page=2' => '第2页',
            '?mod=pending&cid=1&page=1' => '分类第1页'
        ]
    ],
    'rejected' => [
        'name' => '审核不通过',
        'url' => '?mod=rejected',
        'test_urls' => [
            '?mod=rejected&page=1' => '第1页',
            '?mod=rejected&page=2' => '第2页',
            '?mod=rejected&cid=1&page=1' => '分类第1页'
        ]
    ],
    'update' => [
        'name' => '最新收录',
        'url' => '?mod=update',
        'test_urls' => [
            '?mod=update&page=1' => '第1页',
            '?mod=update&page=2' => '第2页',
            '?mod=update&days=7&page=1' => '7天内第1页'
        ]
    ],
    'archives' => [
        'name' => '数据归档',
        'url' => '?mod=archives',
        'test_urls' => [
            '?mod=archives&page=1' => '第1页',
            '?mod=archives&page=2' => '第2页',
            '?mod=archives&date=202501&page=1' => '2025年1月第1页'
        ]
    ],
    'weblink' => [
        'name' => '链接交换',
        'url' => '?mod=weblink',
        'test_urls' => [
            '?mod=weblink&page=1' => '第1页',
            '?mod=weblink&page=2' => '第2页'
        ]
    ],
    'search' => [
        'name' => '搜索结果',
        'url' => '?mod=search',
        'test_urls' => [
            '?mod=search&type=name&query=网站&page=1' => '搜索第1页',
            '?mod=search&type=name&query=网站&page=2' => '搜索第2页'
        ]
    ]
];

echo "<h2>📋 分页功能测试</h2>\n";
echo "<p>以下是各个页面的分页功能测试链接，点击可以直接测试分页是否正常显示：</p>\n";

foreach ($test_pages as $module => $info) {
    echo "<div class='test-item'>\n";
    echo "<h3>📄 {$info['name']} ({$module}.php)</h3>\n";
    
    // 检查模块文件是否使用了showpage函数
    $module_file = "module/{$module}.php";
    if (file_exists($module_file)) {
        $content = file_get_contents($module_file);
        
        if (strpos($content, 'showpage(') !== false) {
            echo "<div class='success'>✅ 使用标准showpage()函数</div>\n";
        } else if (strpos($content, '$showpage') !== false) {
            echo "<div class='warning'>⚠️ 使用自定义分页逻辑</div>\n";
        } else {
            echo "<div class='error'>❌ 未找到分页代码</div>\n";
        }
        
        // 检查是否有分页SEO优化
        if (strpos($content, 'seo_title_suffix') !== false && 
            strpos($content, 'canonical_url') !== false) {
            echo "<div class='success'>✅ 包含分页SEO优化</div>\n";
        } else {
            echo "<div class='warning'>⚠️ 缺少分页SEO优化</div>\n";
        }
    } else {
        echo "<div class='error'>❌ 模块文件不存在</div>\n";
    }
    
    // 测试链接
    echo "<div class='test-links'>\n";
    echo "<strong>测试链接：</strong><br>\n";
    foreach ($info['test_urls'] as $url => $desc) {
        echo "<a href='{$url}' target='_blank'>{$desc}</a>\n";
    }
    echo "</div>\n";
    
    echo "</div>\n";
}

echo "<h2>🔧 修复说明</h2>\n";
echo "<div class='test-item success'>\n";
echo "<h3>已修复的问题</h3>\n";
echo "<ul>\n";
echo "<li><strong>blacklist.php</strong> - 已改为使用标准showpage()函数</li>\n";
echo "<li><strong>vip_list.php</strong> - 已改为使用标准showpage()函数</li>\n";
echo "<li><strong>rejected.php</strong> - 已改为使用标准showpage()函数</li>\n";
echo "<li><strong>pending.php</strong> - 本来就使用标准showpage()函数</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>🧪 测试步骤</h2>\n";
echo "<div class='test-item warning'>\n";
echo "<h3>如何测试分页显示</h3>\n";
echo "<ol>\n";
echo "<li>点击上面的测试链接</li>\n";
echo "<li>检查页面底部是否显示分页导航</li>\n";
echo "<li>检查页面标题是否包含页码信息（如：第2页）</li>\n";
echo "<li>查看页面源码，确认meta标签正确设置</li>\n";
echo "<li>测试分页链接是否可以正常跳转</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>📊 预期效果</h2>\n";
echo "<div class='test-item success'>\n";
echo "<h3>正常的分页显示应该包含：</h3>\n";
echo "<div class='code'>\n";
echo "共 X 条 &laquo; &lsaquo; 1 2 3 4 5 &rsaquo; &raquo;\n";
echo "</div>\n";
echo "<ul>\n";
echo "<li><strong>总数显示：</strong> \"共 X 条\"</li>\n";
echo "<li><strong>首页链接：</strong> &laquo; (双左箭头)</li>\n";
echo "<li><strong>上一页：</strong> &lsaquo; (单左箭头)</li>\n";
echo "<li><strong>页码：</strong> 数字链接，当前页高亮</li>\n";
echo "<li><strong>下一页：</strong> &rsaquo; (单右箭头)</li>\n";
echo "<li><strong>尾页链接：</strong> &raquo; (双右箭头)</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>🎯 SEO优化效果</h2>\n";
echo "<div class='test-item success'>\n";
echo "<h3>分页页面的SEO优化包含：</h3>\n";
echo "<ul>\n";
echo "<li><strong>页面标题：</strong> 包含页码信息，如\"网站目录(第2页) - 95目录网\"</li>\n";
echo "<li><strong>Robots标签：</strong> 第1页index,follow，其他页面noindex,follow</li>\n";
echo "<li><strong>Canonical链接：</strong> 指向当前页面的规范URL</li>\n";
echo "<li><strong>Prev/Next链接：</strong> 帮助搜索引擎理解分页结构</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<hr>\n";
echo "<p><small>测试时间: " . date('Y-m-d H:i:s') . "</small></p>\n";
echo "<p><small>提示：如果分页不显示，可能是因为数据量不足。请确保对应页面有足够的数据来触发分页。</small></p>\n";
?>
