<?php
// 开启所有错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "开始调试...<br/>";

// 1. 检查文件是否存在
echo "<h3>1. 文件存在性检查</h3>";
$files = array(
    'system/search_push.php',
    'source/module/search_push.php', 
    'themes/system/search_push.html'
);

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✓ {$file} 存在<br/>";
    } else {
        echo "✗ {$file} 不存在<br/>";
    }
}

// 2. 尝试直接包含search_push.php文件
echo "<h3>2. 直接包含测试</h3>";

try {
    echo "开始包含 system/search_push.php...<br/>";
    
    // 捕获输出
    ob_start();
    include('system/search_push.php');
    $output = ob_get_clean();
    
    echo "包含成功！<br/>";
    echo "输出长度: " . strlen($output) . " 字符<br/>";
    
    if (empty($output)) {
        echo "⚠ 文件包含成功但没有输出<br/>";
    } else {
        echo "✓ 有输出内容<br/>";
        echo "输出前100字符: " . htmlspecialchars(substr($output, 0, 100)) . "...<br/>";
    }
    
} catch (ParseError $e) {
    echo "✗ 解析错误: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . "<br/>";
    echo "错误行号: " . $e->getLine() . "<br/>";
} catch (Error $e) {
    echo "✗ 致命错误: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . "<br/>";
    echo "错误行号: " . $e->getLine() . "<br/>";
} catch (Exception $e) {
    echo "✗ 异常: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . "<br/>";
    echo "错误行号: " . $e->getLine() . "<br/>";
}

// 3. 检查PHP错误日志
echo "<h3>3. PHP错误日志检查</h3>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "错误日志文件: " . $error_log . "<br/>";
    $log_content = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $log_content), -10);
    echo "最近的错误:<br/>";
    foreach ($recent_errors as $error) {
        if (!empty($error)) {
            echo htmlspecialchars($error) . "<br/>";
        }
    }
} else {
    echo "未找到PHP错误日志文件<br/>";
}

// 4. 检查权限
echo "<h3>4. 权限检查</h3>";
if (isset($_COOKIE['user_auth'])) {
    echo "✓ 用户认证Cookie存在<br/>";
} else {
    echo "⚠ 用户认证Cookie不存在<br/>";
}

// 5. 创建最简单的测试页面
echo "<h3>5. 创建最简单的测试页面</h3>";

$simple_content = '<?php
require("common.php");
echo "<h1>搜索引擎推送测试</h1>";
echo "<p>如果您看到这个页面，说明基本功能正常。</p>";
echo "<p>当前时间: " . date("Y-m-d H:i:s") . "</p>";
?>';

file_put_contents('system/search_push_test.php', $simple_content);
echo "✓ 创建了简单测试页面: system/search_push_test.php<br/>";
echo "<a href='system/search_push_test.php' target='_blank'>点击测试简单页面</a><br/>";

echo "<h3>调试完成</h3>";
echo "<p>请检查上述信息，特别是错误日志部分。</p>";
?>
