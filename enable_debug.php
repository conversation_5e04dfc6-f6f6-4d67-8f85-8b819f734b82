<?php
/**
 * 调试模式开关
 * 临时启用调试模式来排查网站详情页白屏问题
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 定义调试常量
define('DEBUG', true);

echo "<h1>调试模式已启用</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

echo "<h2>可用的调试工具</h2>";
echo "<ul>";
echo "<li><a href='diagnose_siteinfo.php'>完整系统诊断</a> - 检查所有相关文件和函数</li>";
echo "<li><a href='test_siteinfo_simple.php'>简化测试页面</a> - 逐步测试网站详情页逻辑</li>";
echo "<li><a href='?mod=siteinfo&wid=1'>测试网站详情页 (ID=1)</a> - 直接测试</li>";
echo "</ul>";

echo "<h2>调试步骤建议</h2>";
echo "<ol>";
echo "<li>首先运行 <strong>完整系统诊断</strong> 检查基础环境</li>";
echo "<li>如果基础环境正常，运行 <strong>简化测试页面</strong></li>";
echo "<li>最后尝试访问实际的网站详情页</li>";
echo "</ol>";

echo "<h2>常见问题排查</h2>";
echo "<ul>";
echo "<li><strong>白屏无任何输出</strong>：通常是PHP致命错误，检查错误日志</li>";
echo "<li><strong>部分内容显示</strong>：可能是模板问题或JavaScript错误</li>";
echo "<li><strong>数据库错误</strong>：检查数据库连接和表结构</li>";
echo "<li><strong>模板错误</strong>：检查Smarty模板语法和缓存</li>";
echo "</ul>";

// 检查PHP错误日志
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<h2>最近的PHP错误</h2>";
    $log_content = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $log_content), -5);
    
    echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;max-height:200px;overflow:auto;'>";
    foreach ($recent_errors as $error) {
        if (trim($error)) {
            echo htmlspecialchars($error) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p class='info'>无法找到PHP错误日志文件</p>";
}

echo "<hr>";
echo "<p><strong>注意：</strong>调试完成后请删除此文件，避免安全风险。</p>";
?>
