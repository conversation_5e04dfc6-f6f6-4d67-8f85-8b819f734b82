# 自动评论功能使用说明

## 功能概述

为95分类目录网站详情页添加了完整的自动评论功能，当网站从待审核状态变为已审核时，系统会自动添加随机评论，提升网站的活跃度和可信度。

## 主要特性

### ✅ 自动触发机制
- **审核触发**：网站状态从待审核(2)变为已审核(3)时自动触发
- **智能检测**：避免重复添加，已有自动评论的网站不会重复添加
- **批量支持**：支持单个网站和批量网站审核时的自动评论

### 🎯 智能评论生成
- **随机内容**：从25条预设评论模板中随机选择
- **随机用户**：优先使用现有注册会员，不足时使用匿名用户
- **高质量评分**：自动生成4-5星的高质量评分
- **时间分散**：支持1-30分钟的随机时间间隔发布

### 📊 灵活配置
- **数量控制**：可设置每次添加3-10条评论
- **开关控制**：可随时启用或禁用功能
- **延迟发布**：可选择是否启用延迟发布
- **模板管理**：支持自定义评论内容模板

## 安装步骤

### 1. 运行安装脚本
```bash
访问：http://你的域名/install_auto_comments.php
```

安装脚本会自动：
- 创建评论模板数据库表
- 插入25条默认评论模板
- 添加相关配置选项

### 2. 配置功能参数
```bash
访问：系统后台 → 系统设置 → 自动评论设置
```

可配置项目：
- **启用状态**：开启/关闭自动评论功能
- **评论数量**：每次添加的评论数量(3-10条)
- **延迟发布**：是否启用时间间隔发布
- **时间范围**：延迟发布的时间范围(1-1440分钟)

### 3. 管理评论模板
```bash
访问：系统后台 → 系统设置 → 评论模板管理
```

功能包括：
- 添加新的评论模板
- 编辑现有模板内容
- 启用/禁用特定模板
- 批量管理操作

### 4. 测试功能
```bash
访问：http://你的域名/test_auto_comments.php
```

测试内容：
- 检查配置状态
- 预览评论模板
- 测试用户选择
- 实际添加评论测试

## 使用方法

### 自动触发
1. 用户提交网站，状态为"待审核"
2. 管理员在后台审核网站，将状态改为"已审核"
3. 系统自动检测状态变化，触发自动评论功能
4. 根据配置随机添加指定数量的评论

### 手动测试
1. 访问测试页面：`test_auto_comments.php`
2. 选择一个已发布的网站
3. 点击"测试添加评论"按钮
4. 查看添加的评论效果

## 文件结构

### 核心文件
```
module/auto_comments.php              # 自动评论核心功能模块
system/auto_comment_templates.php    # 评论模板管理后台
themes/system/auto_comment_templates.html  # 模板管理界面
```

### 安装和测试文件
```
install_auto_comments.php            # 功能安装脚本
test_auto_comments.php              # 功能测试脚本
data/sql/auto_comment_templates.sql # 数据库结构文件
```

### 配置文件
```
system/option.php                   # 后台配置控制器(已修改)
themes/system/option.html          # 配置界面模板(已修改)
themes/system/admin.html           # 后台导航菜单(已修改)
system/website.php                 # 网站管理模块(已修改)
```

## 数据库结构

### dir_auto_comment_templates 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| template_id | int(10) | 模板ID（主键）|
| content | text | 评论内容 |
| category | varchar(50) | 评论分类 |
| status | tinyint(1) | 状态（1=启用，0=禁用）|
| create_time | int(10) | 创建时间 |
| update_time | int(10) | 更新时间 |

### 配置选项
| 选项名 | 默认值 | 说明 |
|--------|--------|------|
| auto_comment_enabled | yes | 是否启用自动评论 |
| auto_comment_count | 5 | 每次添加评论数量 |
| auto_comment_delay | yes | 是否启用延迟发布 |
| auto_comment_min_delay | 1 | 最小延迟时间(分钟) |
| auto_comment_max_delay | 30 | 最大延迟时间(分钟) |

## 预设评论内容

系统预设了25条高质量的评论模板，包括：

1. 网站设计简洁大方，内容丰富且更新及时，用户体验非常棒，强烈推荐！
2. 界面美观，功能强大，信息准确，是我日常学习和工作的得力助手。
3. 网站加载速度快，资源全面，使用起来非常顺畅，点赞！
4. 内容专业性强，分类清晰，完全满足了我的需求，很赞！
5. 设计贴心，操作便捷，信息准确无误，真是一个优质的网站！

...（共25条）

## 技术特点

### 智能用户选择
- 优先从现有注册会员中随机选择
- 会员不足时自动使用匿名用户
- 匿名用户使用随机昵称（热心网友、匿名用户等）

### 评分算法
- 内容质量：4-5星随机评分
- 服务质量：4-5星随机评分  
- 诚信度：4-5星随机评分
- 偶尔出现3星评分增加真实性

### 时间分散
- 支持1-30分钟的随机时间间隔
- 避免所有评论同时发布
- 增加评论的自然性和真实感

## 注意事项

### 安全考虑
1. **测试完成后删除调试文件**
   - `install_auto_comments.php`
   - `test_auto_comments.php`

2. **定期备份数据库**
   - 特别是评论模板表
   - 避免意外数据丢失

### 使用建议
1. **评论数量适中**：建议每次添加3-5条评论
2. **内容多样化**：定期更新评论模板内容
3. **监控效果**：观察用户反馈和网站活跃度
4. **合理使用**：避免过度使用影响用户体验

### 性能优化
1. **批量操作**：大量网站审核时注意服务器性能
2. **错误处理**：系统会记录错误日志便于排查
3. **缓存更新**：评论添加后会自动更新相关缓存

## 故障排除

### 常见问题

**Q: 自动评论没有生效？**
A: 检查以下项目：
- 功能是否已启用（后台配置）
- 网站状态是否从待审核变为已审核
- 是否已有自动评论（避免重复）
- 查看错误日志

**Q: 评论内容重复？**
A: 
- 检查评论模板数量是否足够
- 添加更多不同的评论模板
- 确保模板状态为启用

**Q: 没有使用会员评论？**
A: 
- 检查是否有足够的注册会员
- 确保会员状态为正常（user_status=1）
- 系统会自动补充匿名用户

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持自动评论功能
- 包含评论模板管理
- 支持后台配置
- 完整的安装和测试工具

## 技术支持

如有问题或建议，请：
1. 查看错误日志文件
2. 运行测试脚本诊断
3. 检查数据库表结构
4. 确认配置选项正确

---

**注意**：此功能旨在提升网站活跃度，请合理使用，确保符合网站运营规范。
