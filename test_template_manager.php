<?php
/**
 * 测试评论模板管理页面
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>测试评论模板管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 测试评论模板管理</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 1. 检查模板表
    echo "<h2>1. 检查模板表</h2>";
    
    $template_table = $DB->table('auto_comment_templates');
    $table_exists = $DB->query("SHOW TABLES LIKE '$template_table'");
    
    if ($DB->num_rows($table_exists)) {
        echo "<div class='success'>✅ 模板表存在: {$template_table}</div>";
        
        $template_count = $DB->get_count($template_table, '1');
        $active_count = $DB->get_count($template_table, 'status = 1');
        
        echo "<div class='info'>总模板数: {$template_count} 条，启用: {$active_count} 条</div>";
    } else {
        echo "<div class='error'>❌ 模板表不存在</div>";
        throw new Exception("模板表不存在，请先运行安装脚本");
    }
    
    // 2. 检查必要文件
    echo "<h2>2. 检查必要文件</h2>";
    
    $files_to_check = array(
        'system/auto_comment_templates.php' => '模板管理控制器',
        'themes/system/auto_comment_templates.html' => '模板管理页面模板',
        'system/common.php' => '公共函数文件'
    );
    
    $all_files_exist = true;
    foreach ($files_to_check as $file => $description) {
        if (file_exists(ROOT_PATH . $file)) {
            echo "<div class='success'>✅ {$description}: {$file}</div>";
        } else {
            echo "<div class='error'>❌ {$description}: {$file} 不存在</div>";
            $all_files_exist = false;
        }
    }
    
    // 3. 检查PHP语法
    echo "<h2>3. 检查PHP语法</h2>";
    
    $template_manager_file = ROOT_PATH . 'system/auto_comment_templates.php';
    
    // 使用php -l 检查语法
    $output = array();
    $return_var = 0;
    exec("php -l \"$template_manager_file\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<div class='success'>✅ PHP语法检查通过</div>";
    } else {
        echo "<div class='error'>❌ PHP语法错误:</div>";
        foreach ($output as $line) {
            echo "<div class='error'>" . htmlspecialchars($line) . "</div>";
        }
    }
    
    // 4. 检查必要函数
    echo "<h2>4. 检查必要函数</h2>";
    
    $required_functions = array(
        'msgbox' => '消息提示函数',
        'smarty_output' => 'Smarty输出函数',
        'addslashes' => '字符串转义函数',
        'showpage' => '分页函数'
    );
    
    foreach ($required_functions as $func => $description) {
        if (function_exists($func)) {
            echo "<div class='success'>✅ {$description}: {$func}()</div>";
        } else {
            echo "<div class='error'>❌ {$description}: {$func}() 不存在</div>";
        }
    }
    
    // 5. 测试数据库操作
    echo "<h2>5. 测试数据库操作</h2>";
    
    try {
        // 测试查询
        $test_query = $DB->query("SELECT COUNT(*) as count FROM $template_table");
        $test_result = $DB->fetch_array($test_query);
        echo "<div class='success'>✅ 数据库查询正常，共 {$test_result['count']} 条记录</div>";
        
        // 测试获取一条记录
        $sample_record = $DB->fetch_one("SELECT * FROM $template_table LIMIT 1");
        if ($sample_record) {
            echo "<div class='success'>✅ 数据记录获取正常</div>";
            echo "<div class='info'>示例记录ID: {$sample_record['template_id']}</div>";
        } else {
            echo "<div class='info'>ℹ️ 表中暂无数据记录</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 数据库操作错误: " . $e->getMessage() . "</div>";
    }
    
    // 6. 检查Smarty模板
    echo "<h2>6. 检查Smarty模板</h2>";
    
    $template_file = ROOT_PATH . 'themes/system/auto_comment_templates.html';
    if (file_exists($template_file)) {
        $template_content = file_get_contents($template_file);
        $template_size = strlen($template_content);
        echo "<div class='success'>✅ 模板文件存在，大小: {$template_size} 字节</div>";
        
        // 检查模板中的关键标签
        $required_tags = array(
            '{$templates}' => '模板数据变量',
            '{$showpage}' => '分页变量',
            'form' => '表单标签'
        );
        
        foreach ($required_tags as $tag => $description) {
            if (strpos($template_content, $tag) !== false) {
                echo "<div class='success'>✅ {$description}: {$tag}</div>";
            } else {
                echo "<div class='error'>❌ {$description}: {$tag} 缺失</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ 模板文件不存在</div>";
    }
    
    // 最终结果
    echo "<h2>测试结果</h2>";
    
    if ($all_files_exist && $return_var === 0) {
        echo "<div class='success'>";
        echo "<h3>🎉 测试通过！</h3>";
        echo "<p>评论模板管理页面应该可以正常工作了。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px; text-align: center;'>";
        echo "<a href='system/auto_comment_templates.php' class='btn' target='_blank'>🚀 打开模板管理页面</a>";
        echo "<br><br>";
        echo "<a href='system/option.php?opt=comment' class='btn' target='_blank'>⚙️ 自动评论设置</a>";
        echo "<a href='test_auto_comments.php' class='btn' target='_blank'>🧪 测试自动评论</a>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 测试失败</h3>";
        echo "<p>发现一些问题，请检查上述错误信息。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='install_auto_comments.php' class='btn'>🔧 重新安装</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 测试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
