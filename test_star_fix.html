<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星级评分修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .star-rating {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            user-select: none;
            display: inline-block;
        }

        .star-rating .star {
            display: inline-block;
            transition: color 0.2s;
            margin-right: 3px;
            cursor: pointer;
            padding: 2px;
            line-height: 1;
            position: relative;
            z-index: 1;
        }

        .star-rating .star:hover,
        .star-rating .star.active {
            color: #ffc107;
            text-shadow: 0 0 3px rgba(255, 193, 7, 0.5);
        }

        .star-rating .star.active ~ .star {
            color: #ddd;
        }

        .rating-item {
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .rating-item label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
            vertical-align: top;
        }

        .current-value {
            margin-left: 15px;
            color: #666;
            font-size: 14px;
        }

        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .stats-item {
            display: inline-block;
            margin-right: 20px;
            text-align: center;
            vertical-align: top;
        }

        .stats-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .stats-value {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .stats-stars {
            font-size: 14px;
            margin-top: 2px;
        }

        .stats-stars span {
            margin-right: 1px;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .star-rating {
                font-size: 28px;
            }
            
            .star-rating .star {
                padding: 5px;
                margin-right: 5px;
            }
        }
    </style>
</head>
<body>
    <h1>星级评分修复测试</h1>
    
    <div class="test-section">
        <h2>1. 评分表单测试</h2>
        <p>请点击下面的星星测试评分功能：</p>
        
        <form id="comment-form">
            <div class="rating-item">
                <label>内容质量：</label>
                <div class="star-rating" data-rating="content_quality">
                    <span class="star" data-value="1">★</span>
                    <span class="star" data-value="2">★</span>
                    <span class="star" data-value="3">★</span>
                    <span class="star" data-value="4">★</span>
                    <span class="star" data-value="5">★</span>
                </div>
                <span class="current-value" id="content_quality_display">当前: 5星</span>
                <input type="hidden" name="content_quality" value="5">
            </div>

            <div class="rating-item">
                <label>网站服务：</label>
                <div class="star-rating" data-rating="service_quality">
                    <span class="star" data-value="1">★</span>
                    <span class="star" data-value="2">★</span>
                    <span class="star" data-value="3">★</span>
                    <span class="star" data-value="4">★</span>
                    <span class="star" data-value="5">★</span>
                </div>
                <span class="current-value" id="service_quality_display">当前: 5星</span>
                <input type="hidden" name="service_quality" value="5">
            </div>

            <div class="rating-item">
                <label>网站诚信：</label>
                <div class="star-rating" data-rating="trust_level">
                    <span class="star" data-value="1">★</span>
                    <span class="star" data-value="2">★</span>
                    <span class="star" data-value="3">★</span>
                    <span class="star" data-value="4">★</span>
                    <span class="star" data-value="5">★</span>
                </div>
                <span class="current-value" id="trust_level_display">当前: 5星</span>
                <input type="hidden" name="trust_level" value="5">
            </div>
        </form>
    </div>

    <div class="test-section">
        <h2>2. 统计显示测试</h2>
        <div id="comment-stats">
            <div class="stats-item">
                <span class="stats-label">总评论数</span>
                <span class="stats-value">5</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">内容质量</span>
                <span class="stats-value">4.2</span>
                <div class="stats-stars"><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ddd;">☆</span></div>
            </div>
            <div class="stats-item">
                <span class="stats-label">网站服务</span>
                <span class="stats-value">3.8</span>
                <div class="stats-stars"><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">☆</span><span style="color: #ddd;">☆</span></div>
            </div>
            <div class="stats-item">
                <span class="stats-label">网站诚信</span>
                <span class="stats-value">4.5</span>
                <div class="stats-stars"><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">★</span><span style="color: #ffc107;">☆</span></div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>3. 获取评分值</h2>
        <button onclick="showCurrentValues()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            显示当前评分
        </button>
        <div id="current-values" style="margin-top: 10px;"></div>
    </div>

    <script>
        // 更新星级显示
        function updateStars(stars, value) {
            stars.forEach((star, index) => {
                if (index < value) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // 初始化星级评分
        function initStarRating() {
            console.log('开始初始化星级评分...');
            const starRatings = document.querySelectorAll('.star-rating');
            console.log(`找到 ${starRatings.length} 个星级评分组件`);
            
            starRatings.forEach((rating, index) => {
                const stars = rating.querySelectorAll('.star');
                const ratingName = rating.getAttribute('data-rating');
                const hiddenInput = document.querySelector(`input[name="${ratingName}"]`);
                const display = document.getElementById(`${ratingName}_display`);
                
                console.log(`处理第 ${index + 1} 个评分组件: ${ratingName}`);
                
                if (!hiddenInput) {
                    console.error(`找不到隐藏输入框: ${ratingName}`);
                    return;
                }
                
                // 设置初始显示状态
                const initialValue = parseInt(hiddenInput.value) || 5;
                updateStars(stars, initialValue);
                
                stars.forEach((star, starIndex) => {
                    // 添加点击事件
                    star.addEventListener('click', function(e) {
                        e.preventDefault();
                        const value = starIndex + 1;
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (display) display.textContent = `当前: ${value}星`;
                        console.log(`点击设置 ${ratingName} 为 ${value} 星`);
                    });
                    
                    // 添加鼠标悬停事件
                    star.addEventListener('mouseover', function() {
                        updateStars(stars, starIndex + 1);
                    });
                    
                    // 添加触摸事件支持移动端
                    star.addEventListener('touchstart', function(e) {
                        e.preventDefault();
                        const value = starIndex + 1;
                        updateStars(stars, value);
                        hiddenInput.value = value;
                        if (display) display.textContent = `当前: ${value}星`;
                        console.log(`触摸设置 ${ratingName} 为 ${value} 星`);
                    });
                });
                
                // 鼠标离开时恢复到当前值
                rating.addEventListener('mouseleave', function() {
                    updateStars(stars, parseInt(hiddenInput.value) || 5);
                });
                
                console.log(`${ratingName} 初始化完成`);
            });
            
            console.log('星级评分初始化完成');
        }

        // 显示当前评分值
        function showCurrentValues() {
            const values = {
                content_quality: document.querySelector('input[name="content_quality"]').value,
                service_quality: document.querySelector('input[name="service_quality"]').value,
                trust_level: document.querySelector('input[name="trust_level"]').value
            };
            
            document.getElementById('current-values').innerHTML = `
                <h4>当前评分值：</h4>
                <p><strong>内容质量：</strong> ${values.content_quality} 星</p>
                <p><strong>网站服务：</strong> ${values.service_quality} 星</p>
                <p><strong>网站诚信：</strong> ${values.trust_level} 星</p>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            initStarRating();
        });
    </script>
</body>
</html>
