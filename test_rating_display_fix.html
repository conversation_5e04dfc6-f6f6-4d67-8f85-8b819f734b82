<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评分显示修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 复制修复后的评论评分样式 */
        .comment-ratings {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .rating-row {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }

        /* 桌面端：显示span布局，隐藏rating-row */
        @media (min-width: 769px) {
            .comment-ratings > span {
                display: inline;
                margin-right: 15px;
                color: #666;
            }
            
            .comment-ratings .rating-row {
                display: none;
            }
        }

        /* 移动端：隐藏span布局，显示rating-row三行布局 */
        @media (max-width: 768px) {
            .comment-ratings {
                margin-bottom: 12px;
            }
            
            /* 只隐藏直接子级的span（评分标签），不影响星星的span */
            .comment-ratings > span {
                display: none;
            }
            
            .comment-ratings .rating-row {
                display: block;
                margin-bottom: 6px;
                font-size: 13px;
                width: 100%;
                color: #666;
            }
            
            /* 确保星星的span正常显示 */
            .comment-ratings .rating-row span {
                display: inline;
                margin-right: 1px;
            }
        }
        
        .star {
            color: #ffc107;
        }
        
        .star-empty {
            color: #ddd;
        }
        
        .device-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .comment-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .comment-header {
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .comment-user {
            font-weight: bold;
            color: #333;
        }
        
        .comment-time {
            color: #999;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>评分显示修复测试</h1>
        
        <div class="device-info">
            <strong>当前设备信息：</strong>
            <span id="device-info"></span>
        </div>
        
        <div class="test-section">
            <div class="test-title">1. 修复后的评论评分显示</div>
            <div class="comment-item">
                <div class="comment-header">
                    <span class="comment-user">测试用户</span>
                    <span class="comment-time">2024-01-15 10:30</span>
                </div>
                <div class="comment-ratings">
                    <!-- 桌面端布局 -->
                    <span>内容质量：<span class="star">★★★★★</span></span>
                    <span>网站服务：<span class="star">★★★★</span><span class="star-empty">☆</span></span>
                    <span>网站诚信：<span class="star">★★★★★</span></span>
                    <!-- 移动端布局 -->
                    <div class="rating-row">内容质量：<span class="star">★★★★★</span></div>
                    <div class="rating-row">网站服务：<span class="star">★★★★</span><span class="star-empty">☆</span></div>
                    <div class="rating-row">网站诚信：<span class="star">★★★★★</span></div>
                </div>
                <div class="comment-content">这是一个测试评论，用来验证评分显示是否正常。</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 另一个评论示例</div>
            <div class="comment-item">
                <div class="comment-header">
                    <span class="comment-user">匿名用户</span>
                    <span class="comment-time">2024-01-15 09:15</span>
                </div>
                <div class="comment-ratings">
                    <!-- 桌面端布局 -->
                    <span>内容质量：<span class="star">★★★</span><span class="star-empty">☆☆</span></span>
                    <span>网站服务：<span class="star">★★★★</span><span class="star-empty">☆</span></span>
                    <span>网站诚信：<span class="star">★★★★★</span></span>
                    <!-- 移动端布局 -->
                    <div class="rating-row">内容质量：<span class="star">★★★</span><span class="star-empty">☆☆</span></div>
                    <div class="rating-row">网站服务：<span class="star">★★★★</span><span class="star-empty">☆</span></div>
                    <div class="rating-row">网站诚信：<span class="star">★★★★★</span></div>
                </div>
                <div class="comment-content">网站内容还不错，但是服务有待提升。</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 修复说明</div>
            <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                <h4 style="margin-top: 0; color: #155724;">✅ 问题已修复</h4>
                <ul style="margin-bottom: 0;">
                    <li><strong>问题原因：</strong>CSS选择器 <code>.comment-ratings span</code> 隐藏了所有span，包括星星</li>
                    <li><strong>修复方案：</strong>使用 <code>.comment-ratings > span</code> 只隐藏直接子级span</li>
                    <li><strong>新增规则：</strong><code>.comment-ratings .rating-row span</code> 确保星星正常显示</li>
                </ul>
            </div>
            
            <h4>显示逻辑：</h4>
            <ul>
                <li><strong>桌面端（>768px）：</strong>显示横向span布局，隐藏rating-row</li>
                <li><strong>移动端（≤768px）：</strong>隐藏span布局，显示三行rating-row布局</li>
                <li><strong>星星显示：</strong>在任何情况下都正常显示</li>
            </ul>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. CSS关键修复</div>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
/* 移动端修复 */
@media (max-width: 768px) {
    /* 只隐藏直接子级的span（评分标签） */
    .comment-ratings > span {
        display: none;
    }
    
    /* 确保星星的span正常显示 */
    .comment-ratings .rating-row span {
        display: inline;
        margin-right: 1px;
    }
}</pre>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const width = window.innerWidth;
            let deviceType = '';
            
            if (width <= 480) {
                deviceType = '小屏手机';
            } else if (width <= 768) {
                deviceType = '移动设备';
            } else if (width <= 1024) {
                deviceType = '平板设备';
            } else {
                deviceType = '桌面设备';
            }
            
            document.getElementById('device-info').textContent = 
                `屏幕宽度: ${width}px (${deviceType})`;
        }
        
        // 页面加载和窗口大小改变时更新设备信息
        window.addEventListener('load', updateDeviceInfo);
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
