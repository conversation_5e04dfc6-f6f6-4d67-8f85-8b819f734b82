<?php
/**
 * 用户功能测试脚本
 */

echo "<h1>用户功能测试</h1>";

try {
    // 引入初始化文件
    require_once('./source/init.php');
    require_once('./source/module/user.php');
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
    echo "✅ 初始化文件和用户模块加载成功";
    echo "</div>";
    
    // 检查必要的函数是否存在
    echo "<h2>函数可用性检查</h2>";
    $required_functions = array(
        'check_user_login',
        'get_one_user',
        'get_user_list',
        'authcode',
        'get_client_ip'
    );
    
    foreach ($required_functions as $func) {
        $status = function_exists($func) ? "✅ 存在" : "❌ 不存在";
        $color = function_exists($func) ? "green" : "red";
        echo "<div style='color: $color; margin: 5px 0;'>$status - $func()</div>";
    }
    
    // 检查全局变量
    echo "<h2>全局变量检查</h2>";
    $global_vars = array('DB', 'user_types', 'options');
    
    foreach ($global_vars as $var) {
        $exists = isset($GLOBALS[$var]);
        $status = $exists ? "✅ 存在" : "❌ 不存在";
        $color = $exists ? "green" : "red";
        echo "<div style='color: $color; margin: 5px 0;'>$status - \$$var</div>";
        
        if ($exists && $var == 'user_types') {
            echo "<div style='margin-left: 20px; font-size: 12px; color: #666;'>";
            echo "用户类型：" . implode(', ', array_keys($user_types));
            echo "</div>";
        }
    }
    
    // 测试用户登录状态检查
    echo "<h2>用户登录状态测试</h2>";
    $auth_cookie = isset($_COOKIE['auth_cookie']) ? $_COOKIE['auth_cookie'] : '';
    
    if (empty($auth_cookie)) {
        echo "<div style='color: orange; padding: 10px; border: 1px solid orange; border-radius: 4px; margin: 10px 0;'>";
        echo "⚠️ 没有找到登录Cookie (auth_cookie)";
        echo "</div>";
        echo "<p>当前状态：未登录（匿名用户）</p>";
        
        // 测试空参数的情况
        $user_info = check_user_login('');
        if (empty($user_info)) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ check_user_login() 函数对空参数处理正常";
            echo "</div>";
        }
    } else {
        echo "<div style='color: blue; padding: 10px; border: 1px solid blue; border-radius: 4px; margin: 10px 0;'>";
        echo "📝 找到登录Cookie: " . substr($auth_cookie, 0, 20) . "...";
        echo "</div>";
        
        $user_info = check_user_login($auth_cookie);
        
        if (!empty($user_info)) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ 用户已登录";
            echo "</div>";
            
            echo "<h3>用户信息：</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            foreach ($user_info as $key => $value) {
                echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
            echo "❌ Cookie存在但用户验证失败";
            echo "</div>";
        }
    }
    
    // 测试IP获取
    echo "<h2>IP地址获取测试</h2>";
    $client_ip = get_client_ip();
    echo "<p><strong>当前IP地址：</strong> $client_ip</p>";
    
    // 测试authcode函数
    echo "<h2>编码函数测试</h2>";
    $test_string = "test_data|123|456";
    $encoded = authcode($test_string, 'ENCODE');
    $decoded = authcode($encoded, 'DECODE');
    
    echo "<p><strong>原始数据：</strong> $test_string</p>";
    echo "<p><strong>编码后：</strong> " . substr($encoded, 0, 30) . "...</p>";
    echo "<p><strong>解码后：</strong> $decoded</p>";
    
    if ($test_string === $decoded) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
        echo "✅ authcode 编码解码功能正常";
        echo "</div>";
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ authcode 编码解码功能异常";
        echo "</div>";
    }
    
    // 测试数据库用户查询
    echo "<h2>数据库用户查询测试</h2>";
    $users_sql = "SELECT user_id, user_email, nick_name, user_type FROM " . $DB->table('users') . " LIMIT 3";
    $users_result = $DB->query($users_sql);
    
    if ($DB->num_rows($users_result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>用户ID</th><th>邮箱</th><th>昵称</th><th>类型</th></tr>";
        while ($user = $DB->fetch_array($users_result)) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['user_email']}</td>";
            echo "<td>{$user['nick_name']}</td>";
            echo "<td>{$user['user_type']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>没有找到用户数据</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
    echo "❌ 错误：" . $e->getMessage();
    echo "</div>";
}

echo "<h2>登录测试</h2>";
echo "<p>如果您想测试会员评论功能，请先登录：</p>";
echo "<p><a href='member/?mod=login' target='_blank'>会员登录</a></p>";
echo "<p><a href='member/?mod=register' target='_blank'>会员注册</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
