<?php
/**
 * 测试模板语法修复
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>测试模板语法修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        .btn-large { font-size: 16px; padding: 15px 30px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 测试模板语法修复</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 1. 检查模板文件语法
    echo "<h2>1. 检查模板文件语法</h2>";
    
    $template_file = ROOT_PATH . 'themes/system/auto_comment_templates.html';
    if (file_exists($template_file)) {
        $template_content = file_get_contents($template_file);
        echo "<div class='success'>✅ 模板文件存在</div>";
        
        // 检查是否使用了正确的语法
        $old_syntax_count = preg_match_all('/\{\$[^}]+\}/', $template_content);
        $new_syntax_count = preg_match_all('/\{#\$[^}]+#\}/', $template_content);
        
        echo "<div class='info'>旧语法 {\$...} 数量: {$old_syntax_count}</div>";
        echo "<div class='info'>新语法 {#\$...#} 数量: {$new_syntax_count}</div>";
        
        if ($old_syntax_count == 0) {
            echo "<div class='success'>✅ 模板语法已正确修复</div>";
        } else {
            echo "<div class='error'>❌ 仍有旧语法需要修复</div>";
        }
    } else {
        echo "<div class='error'>❌ 模板文件不存在</div>";
    }
    
    // 2. 检查控制器文件
    echo "<h2>2. 检查控制器文件</h2>";
    
    $controller_file = ROOT_PATH . 'system/auto_comment_templates.php';
    if (file_exists($controller_file)) {
        echo "<div class='success'>✅ 控制器文件存在</div>";
        
        // 检查PHP语法
        $output = array();
        $return_var = 0;
        exec("php -l \"$controller_file\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<div class='success'>✅ PHP语法检查通过</div>";
        } else {
            echo "<div class='error'>❌ PHP语法错误:</div>";
            foreach ($output as $line) {
                echo "<div class='error'>" . htmlspecialchars($line) . "</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ 控制器文件不存在</div>";
    }
    
    // 3. 检查数据库表
    echo "<h2>3. 检查数据库表</h2>";
    
    $template_table = $DB->table('auto_comment_templates');
    $table_exists = $DB->query("SHOW TABLES LIKE '$template_table'");
    
    if ($DB->num_rows($table_exists)) {
        $template_count = $DB->get_count($template_table, '1');
        echo "<div class='success'>✅ 数据库表存在，共 {$template_count} 条记录</div>";
    } else {
        echo "<div class='error'>❌ 数据库表不存在</div>";
    }
    
    // 4. 模拟访问测试
    echo "<h2>4. 模拟访问测试</h2>";
    
    // 设置必要的变量来模拟控制器环境
    $_GET['act'] = 'list';
    $action = 'list';
    $fileurl = 'auto_comment_templates.php';
    $tempfile = 'auto_comment_templates.html';
    $table = $DB->table('auto_comment_templates');
    $pagesize = 20;
    $curpage = 1;
    $start = 0;
    $pagetitle = '自动评论模板管理';
    
    try {
        // 模拟数据查询
        $keywords = '';
        $status = -1;
        $where = '1';
        
        $sql = "SELECT * FROM $table WHERE $where ORDER BY template_id DESC LIMIT $start, $pagesize";
        $query = $DB->query($sql);
        $templates = array();
        
        while ($row = $DB->fetch_array($query)) {
            $row['content_preview'] = mb_strlen($row['content']) > 50 ? mb_substr($row['content'], 0, 50) . '...' : $row['content'];
            $row['status_text'] = $row['status'] == 1 ? '<span style="color: green;">启用</span>' : '<span style="color: red;">禁用</span>';
            $row['create_time_text'] = date('Y-m-d H:i:s', $row['create_time']);
            $row['operate'] = '<a href="'.$fileurl.'?act=edit&template_id='.$row['template_id'].'">编辑</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?act=del&template_id='.$row['template_id'].'" onClick="return confirm(\'确认删除此模板吗？\');">删除</a>';
            $templates[] = $row;
        }
        
        $total = $DB->get_count($table, $where);
        
        echo "<div class='success'>✅ 数据查询正常，找到 {$total} 条记录</div>";
        echo "<div class='info'>当前页显示 " . count($templates) . " 条记录</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 数据查询失败: " . $e->getMessage() . "</div>";
    }
    
    // 最终结果
    echo "<h2>测试结果</h2>";
    
    if ($old_syntax_count == 0 && $return_var === 0 && $DB->num_rows($table_exists)) {
        echo "<div class='success'>";
        echo "<h3>🎉 模板修复成功！</h3>";
        echo "<p>评论模板管理页面现在应该可以正常显示了。</p>";
        echo "<ul>";
        echo "<li>✅ 模板语法已修复为正确格式</li>";
        echo "<li>✅ PHP控制器语法正确</li>";
        echo "<li>✅ 数据库表存在且有数据</li>";
        echo "<li>✅ 数据查询功能正常</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h4>📋 现在可以正常使用</h4>";
        echo "<ol>";
        echo "<li>点击下方按钮访问模板管理页面</li>";
        echo "<li>查看、添加、编辑评论模板</li>";
        echo "<li>启用或禁用特定模板</li>";
        echo "<li>批量管理操作</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px; text-align: center;'>";
        echo "<a href='system/auto_comment_templates.php' class='btn btn-large' target='_blank'>🚀 打开模板管理页面</a>";
        echo "<br><br>";
        echo "<a href='system/option.php?opt=comment' class='btn' target='_blank'>⚙️ 自动评论设置</a>";
        echo "<a href='test_auto_comments.php' class='btn' target='_blank'>🧪 测试自动评论</a>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ 仍有问题需要解决</h3>";
        echo "<p>请检查上述错误信息并进行修复。</p>";
        echo "</div>";
        
        echo "<div style='margin-top: 30px;'>";
        echo "<a href='install_auto_comments.php' class='btn'>🔧 重新安装</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 测试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
