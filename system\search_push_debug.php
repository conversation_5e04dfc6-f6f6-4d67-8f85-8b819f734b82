<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始调试 search_push.php...<br/>";

try {
    echo "1. 加载 common.php...<br/>";
    require('common.php');
    echo "✓ common.php 加载成功<br/>";
    
    echo "2. 设置基本变量...<br/>";
    $fileurl = 'search_push.php';
    $tempfile = 'search_push.html';
    echo "✓ 基本变量设置完成<br/>";
    
    echo "3. 加载配置文件...<br/>";
    if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
    require(ROOT_PATH.'data/static/options.php');
    $options = $static_data;
    echo "✓ 配置文件加载成功<br/>";
    echo "网站URL: " . $options['site_url'] . "<br/>";
    
    echo "4. 加载搜索推送模块...<br/>";
    require(APP_PATH.'module/search_push.php');
    echo "✓ 搜索推送模块加载成功<br/>";
    
    echo "5. 检查函数是否存在...<br/>";
    if (function_exists('get_push_config')) {
        echo "✓ get_push_config 函数存在<br/>";
    } else {
        echo "✗ get_push_config 函数不存在<br/>";
        exit;
    }
    
    echo "6. 设置action...<br/>";
    if (!isset($action)) $action = 'config';
    echo "✓ action = " . $action . "<br/>";
    
    echo "7. 测试配置获取...<br/>";
    $config = get_push_config();
    echo "✓ 配置获取成功<br/>";
    
    echo "8. 设置页面变量...<br/>";
    $pagetitle = '搜索引擎推送配置';
    
    echo "9. 分配Smarty变量...<br/>";
    $smarty->assign('config', $config);
    $smarty->assign('action', $action);
    $smarty->assign('fileurl', $fileurl);
    $smarty->assign('pagetitle', $pagetitle);
    echo "✓ Smarty变量分配完成<br/>";
    
    echo "10. 检查模板文件...<br/>";
    if (function_exists('template_exists')) {
        template_exists($tempfile);
        echo "✓ 模板文件验证通过<br/>";
    }
    
    echo "11. 输出模板...<br/>";
    smarty_output($tempfile);
    echo "✓ 模板输出完成<br/>";
    
} catch (Exception $e) {
    echo "✗ 错误: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . "<br/>";
    echo "错误行号: " . $e->getLine() . "<br/>";
    echo "错误堆栈:<br/>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "✗ 致命错误: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . "<br/>";
    echo "错误行号: " . $e->getLine() . "<br/>";
}
?>
