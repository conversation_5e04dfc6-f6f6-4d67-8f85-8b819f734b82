<?php
/**
 * 测试后台访问权限
 */

echo "<h2>后台访问权限测试</h2>";

// 设置基本常量
define('IN_ADMIN', TRUE);
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "<h3>1. 基本信息</h3>";
echo "ROOT_PATH: " . ROOT_PATH . "<br/>";
echo "APP_PATH: " . APP_PATH . "<br/>";

// 加载初始化文件
try {
    require(APP_PATH.'init.php');
    echo "✓ init.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ init.php 加载失败: " . $e->getMessage() . "<br/>";
    exit;
}

// 加载静态模块
try {
    require(APP_PATH.'module/static.php');
    echo "✓ static.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ static.php 加载失败: " . $e->getMessage() . "<br/>";
}

// 加载函数文件
try {
    require('./system/function.php');
    echo "✓ function.php 加载成功<br/>";
} catch (Exception $e) {
    echo "✗ function.php 加载失败: " . $e->getMessage() . "<br/>";
}

echo "<h3>2. 数据库连接测试</h3>";
if (isset($DB)) {
    try {
        $table = $DB->table('users');
        $result = $DB->get_one("SELECT COUNT(*) as count FROM $table WHERE user_type='admin'");
        echo "✓ 数据库连接正常，管理员用户数量: " . $result['count'] . "<br/>";
    } catch (Exception $e) {
        echo "✗ 数据库查询失败: " . $e->getMessage() . "<br/>";
    }
} else {
    echo "✗ 数据库对象不存在<br/>";
}

echo "<h3>3. Cookie检查</h3>";
if (isset($_COOKIE['user_auth'])) {
    echo "✓ 用户认证Cookie存在<br/>";
    echo "Cookie值: " . substr($_COOKIE['user_auth'], 0, 20) . "...<br/>";
} else {
    echo "⚠ 用户认证Cookie不存在，需要先登录<br/>";
    echo "<a href='system/login.php' target='_blank'>点击这里登录后台</a><br/>";
}

echo "<h3>4. 搜索推送文件检查</h3>";
$files = array(
    'system/search_push.php' => '后台管理文件',
    'source/module/search_push.php' => '推送模块文件',
    'themes/system/search_push.html' => '模板文件'
);

foreach ($files as $file => $desc) {
    if (file_exists($file)) {
        echo "✓ {$desc} 存在 ({$file})<br/>";
    } else {
        echo "✗ {$desc} 不存在 ({$file})<br/>";
    }
}

echo "<h3>5. 访问测试</h3>";
echo "<p>如果您已经登录后台，可以点击以下链接测试：</p>";
echo "<a href='system/search_push.php' target='_blank'>搜索引擎推送管理</a><br/>";

echo "<h3>6. 登录信息</h3>";
echo "<p>如果还没有登录，请先访问：</p>";
echo "<a href='system/login.php' target='_blank'>后台登录</a><br/>";
echo "<p>登录后再访问搜索引擎推送功能。</p>";
?>
