<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>登录后诊断</h2>";

try {
    echo "1. 加载 common.php...<br/>";
    require('common.php');
    echo "✓ common.php 加载成功<br/>";
    
    echo "2. 检查登录状态...<br/>";
    if (isset($myself) && !empty($myself)) {
        echo "✓ 用户已登录<br/>";
        echo "用户ID: " . $myself['user_id'] . "<br/>";
        echo "用户邮箱: " . $myself['user_email'] . "<br/>";
    } else {
        echo "✗ 用户未登录<br/>";
        exit;
    }
    
    echo "3. 检查基本变量...<br/>";
    echo "ROOT_PATH: " . ROOT_PATH . "<br/>";
    echo "APP_PATH: " . APP_PATH . "<br/>";
    
    echo "4. 检查数据库连接...<br/>";
    if (isset($DB)) {
        echo "✓ 数据库对象存在<br/>";
        $table = $DB->table('options');
        $result = $DB->get_one("SELECT COUNT(*) as count FROM $table");
        echo "✓ 数据库查询正常，options表有 " . $result['count'] . " 条记录<br/>";
    } else {
        echo "✗ 数据库对象不存在<br/>";
    }
    
    echo "5. 检查Smarty对象...<br/>";
    if (isset($smarty)) {
        echo "✓ Smarty对象存在<br/>";
    } else {
        echo "✗ Smarty对象不存在<br/>";
    }
    
    echo "6. 加载配置文件...<br/>";
    if (!defined('IN_HANFOX')) define('IN_HANFOX', true);
    require(ROOT_PATH.'data/static/options.php');
    $options = $static_data;
    echo "✓ 配置文件加载成功<br/>";
    echo "网站URL: " . $options['site_url'] . "<br/>";
    
    echo "7. 检查模块文件...<br/>";
    $module_file = APP_PATH.'module/search_push.php';
    if (file_exists($module_file)) {
        echo "✓ 模块文件存在: " . $module_file . "<br/>";
        
        // 检查文件语法
        $syntax_output = shell_exec("php -l " . escapeshellarg($module_file) . " 2>&1");
        if (strpos($syntax_output, 'No syntax errors') !== false) {
            echo "✓ 模块文件语法正确<br/>";
        } else {
            echo "✗ 模块文件语法错误: " . htmlspecialchars($syntax_output) . "<br/>";
            exit;
        }
        
        echo "8. 加载模块文件...<br/>";
        require($module_file);
        echo "✓ 模块文件加载成功<br/>";
        
    } else {
        echo "✗ 模块文件不存在: " . $module_file . "<br/>";
        exit;
    }
    
    echo "9. 检查函数定义...<br/>";
    $functions = array('get_push_config', 'save_push_config', 'get_all_urls');
    foreach ($functions as $func) {
        if (function_exists($func)) {
            echo "✓ 函数 {$func} 存在<br/>";
        } else {
            echo "✗ 函数 {$func} 不存在<br/>";
        }
    }
    
    echo "10. 测试核心函数...<br/>";
    if (function_exists('get_push_config')) {
        try {
            $config = get_push_config();
            echo "✓ get_push_config 执行成功<br/>";
        } catch (Exception $e) {
            echo "✗ get_push_config 执行失败: " . $e->getMessage() . "<br/>";
        }
    }
    
    if (function_exists('get_all_urls')) {
        try {
            $urls = get_all_urls();
            echo "✓ get_all_urls 执行成功，获取到 " . count($urls) . " 个URL<br/>";
        } catch (Exception $e) {
            echo "✗ get_all_urls 执行失败: " . $e->getMessage() . "<br/>";
        }
    }
    
    echo "11. 检查模板文件...<br/>";
    $template_file = ROOT_PATH . 'themes/system/search_push.html';
    if (file_exists($template_file)) {
        echo "✓ 模板文件存在<br/>";
        
        if (function_exists('template_exists')) {
            try {
                template_exists('search_push.html');
                echo "✓ 模板文件验证通过<br/>";
            } catch (Exception $e) {
                echo "✗ 模板文件验证失败: " . $e->getMessage() . "<br/>";
            }
        }
    } else {
        echo "✗ 模板文件不存在: " . $template_file . "<br/>";
    }
    
    echo "12. 模拟完整页面处理...<br/>";
    
    // 设置页面变量
    $fileurl = 'search_push.php';
    $tempfile = 'search_push.html';
    $action = 'config';
    $pagetitle = '搜索引擎推送配置';
    
    // 获取配置
    $config = get_push_config();
    
    // 分配变量
    $smarty->assign('config', $config);
    $smarty->assign('action', $action);
    $smarty->assign('fileurl', $fileurl);
    $smarty->assign('pagetitle', $pagetitle);
    
    echo "✓ 页面变量设置完成<br/>";
    
    echo "13. 尝试渲染模板...<br/>";
    ob_start();
    smarty_output($tempfile);
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "✓ 模板渲染成功！输出长度: " . strlen($output) . " 字符<br/>";
        echo "<h4>实际输出内容:</h4>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow: auto;'>";
        echo htmlspecialchars(substr($output, 0, 1000));
        if (strlen($output) > 1000) echo "...";
        echo "</div>";
    } else {
        echo "✗ 模板渲染失败，无输出<br/>";
    }
    
} catch (ParseError $e) {
    echo "✗ 解析错误: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br/>";
} catch (Error $e) {
    echo "✗ 致命错误: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br/>";
} catch (Exception $e) {
    echo "✗ 异常: " . $e->getMessage() . "<br/>";
    echo "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br/>";
}

echo "<h3>诊断完成</h3>";
echo "<p>如果上述所有检查都通过，但原页面仍然白屏，可能是以下原因：</p>";
echo "<ul>";
echo "<li>浏览器缓存问题</li>";
echo "<li>服务器配置问题</li>";
echo "<li>文件权限问题</li>";
echo "<li>PHP配置问题</li>";
echo "</ul>";
?>
