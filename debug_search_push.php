<?php
/**
 * 搜索引擎推送功能调试页面
 */

echo "<h2>搜索引擎推送功能调试</h2>";

// 1. 检查基本常量
echo "<h3>1. 基本常量检查</h3>";
echo "IN_IWEBDIR: " . (defined('IN_IWEBDIR') ? 'YES' : 'NO') . "<br/>";
echo "ROOT_PATH: " . (defined('ROOT_PATH') ? ROOT_PATH : 'NOT DEFINED') . "<br/>";
echo "APP_PATH: " . (defined('APP_PATH') ? APP_PATH : 'NOT DEFINED') . "<br/>";

// 2. 设置常量
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "设置后 ROOT_PATH: " . ROOT_PATH . "<br/>";
echo "设置后 APP_PATH: " . APP_PATH . "<br/>";

// 3. 检查文件是否存在
echo "<h3>2. 文件存在性检查</h3>";
$files = array(
    APP_PATH.'init.php',
    APP_PATH.'module/search_push.php',
    ROOT_PATH.'data/static/options.php',
    ROOT_PATH.'system/search_push.php',
    ROOT_PATH.'themes/system/search_push.html'
);

foreach ($files as $file) {
    echo $file . ": " . (file_exists($file) ? 'EXISTS' : 'NOT FOUND') . "<br/>";
}

// 4. 尝试加载init.php
echo "<h3>3. 加载init.php</h3>";
try {
    require_once(APP_PATH.'init.php');
    echo "✓ init.php 加载成功<br/>";
    
    // 检查数据库对象
    if (isset($DB)) {
        echo "✓ 数据库对象存在<br/>";
        echo "数据库对象类型: " . get_class($DB) . "<br/>";
    } else {
        echo "✗ 数据库对象不存在<br/>";
    }
    
    // 检查Smarty对象
    if (isset($smarty)) {
        echo "✓ Smarty对象存在<br/>";
        echo "Smarty对象类型: " . get_class($smarty) . "<br/>";
    } else {
        echo "✗ Smarty对象不存在<br/>";
    }
    
} catch (Exception $e) {
    echo "✗ init.php 加载失败: " . $e->getMessage() . "<br/>";
}

// 5. 尝试加载配置文件
echo "<h3>4. 加载配置文件</h3>";
try {
    define('IN_HANFOX', true);
    require(ROOT_PATH.'data/static/options.php');
    
    if (isset($static_data)) {
        echo "✓ 配置数据加载成功<br/>";
        echo "配置项数量: " . count($static_data) . "<br/>";
        echo "网站URL: " . (isset($static_data['site_url']) ? $static_data['site_url'] : 'NOT SET') . "<br/>";
        $options = $static_data;
    } else {
        echo "✗ 配置数据不存在<br/>";
    }
} catch (Exception $e) {
    echo "✗ 配置文件加载失败: " . $e->getMessage() . "<br/>";
}

// 6. 尝试加载搜索推送模块
echo "<h3>5. 加载搜索推送模块</h3>";
try {
    require_once(APP_PATH.'module/search_push.php');
    echo "✓ 搜索推送模块加载成功<br/>";
    
    // 检查函数是否存在
    $functions = array('get_all_urls', 'get_push_config', 'save_push_config');
    foreach ($functions as $func) {
        echo "函数 {$func}: " . (function_exists($func) ? 'EXISTS' : 'NOT FOUND') . "<br/>";
    }
    
} catch (Exception $e) {
    echo "✗ 搜索推送模块加载失败: " . $e->getMessage() . "<br/>";
}

// 7. 测试数据库连接
echo "<h3>6. 测试数据库连接</h3>";
if (isset($DB)) {
    try {
        $table = $DB->table('options');
        echo "Options表名: " . $table . "<br/>";
        
        $result = $DB->get_one("SELECT COUNT(*) as count FROM $table");
        echo "✓ 数据库查询成功，options表有 " . $result['count'] . " 条记录<br/>";
        
        // 测试获取配置
        if (function_exists('get_push_config')) {
            $config = get_push_config();
            echo "✓ 获取推送配置成功<br/>";
            echo "百度Token: " . (empty($config['baidu_token']) ? '未配置' : '已配置') . "<br/>";
        }
        
    } catch (Exception $e) {
        echo "✗ 数据库操作失败: " . $e->getMessage() . "<br/>";
    }
}

// 8. 测试URL获取
echo "<h3>7. 测试URL获取</h3>";
if (function_exists('get_all_urls') && isset($options)) {
    try {
        $urls = get_all_urls();
        echo "✓ 获取URL成功，共 " . count($urls) . " 个<br/>";
        if (count($urls) > 0) {
            echo "第一个URL: " . htmlspecialchars($urls[0]) . "<br/>";
        }
    } catch (Exception $e) {
        echo "✗ 获取URL失败: " . $e->getMessage() . "<br/>";
    }
}

// 9. 检查后台文件访问
echo "<h3>8. 后台文件访问测试</h3>";
$admin_url = "system/search_push.php";
echo "后台文件路径: <a href='{$admin_url}' target='_blank'>{$admin_url}</a><br/>";

// 10. 检查模板文件
echo "<h3>9. 模板文件检查</h3>";
$template_file = ROOT_PATH . 'themes/system/search_push.html';
if (file_exists($template_file)) {
    echo "✓ 模板文件存在<br/>";
    echo "文件大小: " . filesize($template_file) . " 字节<br/>";
    
    // 检查模板内容
    $content = file_get_contents($template_file);
    if (strpos($content, '{#include file="header.html"#}') !== false) {
        echo "✓ 模板语法正确<br/>";
    } else {
        echo "⚠ 模板语法可能有问题<br/>";
    }
} else {
    echo "✗ 模板文件不存在<br/>";
}

echo "<h3>调试完成</h3>";
echo "<p>请根据上述信息检查问题所在。</p>";
?>
