<?php
// 测试伪静态规则
echo "测试伪静态规则\n";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "QUERY_STRING: " . $_SERVER['QUERY_STRING'] . "\n";
echo "GET参数: " . print_r($_GET, true) . "\n";

// 检查模块文件是否存在
$module = $_GET['mod'] ?? 'index';
$modpath = './module/' . $module . '.php';
echo "模块: $module\n";
echo "模块文件路径: $modpath\n";
echo "模块文件存在: " . (file_exists($modpath) ? '是' : '否') . "\n";

// 检查模板文件是否存在
$tempfile = './themes/default/' . $module . '.html';
echo "模板文件路径: $tempfile\n";
echo "模板文件存在: " . (file_exists($tempfile) ? '是' : '否') . "\n";
?>
