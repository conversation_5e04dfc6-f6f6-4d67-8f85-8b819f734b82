<?php
/**
 * 搜索引擎推送功能修复脚本
 * 用于修复可能的权限和配置问题
 */

// 设置基本常量
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

echo "<h2>搜索引擎推送功能修复脚本</h2>";

// 1. 检查并创建必要的目录
echo "<h3>1. 检查目录权限</h3>";

$dirs_to_check = array(
    ROOT_PATH . 'data/static/',
    ROOT_PATH . 'themes/system/',
    ROOT_PATH . 'system/',
    ROOT_PATH . 'source/module/'
);

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✓ 目录 {$dir} 可写<br/>";
        } else {
            echo "⚠ 目录 {$dir} 不可写，尝试修复...<br/>";
            if (chmod($dir, 0755)) {
                echo "✓ 目录权限修复成功<br/>";
            } else {
                echo "✗ 目录权限修复失败<br/>";
            }
        }
    } else {
        echo "✗ 目录 {$dir} 不存在<br/>";
    }
}

// 2. 检查文件是否存在
echo "<h3>2. 检查必要文件</h3>";

$files_to_check = array(
    'system/search_push.php' => '后台管理文件',
    'source/module/search_push.php' => '推送功能模块',
    'themes/system/search_push.html' => '模板文件',
    'cron_auto_push.php' => '定时任务文件'
);

foreach ($files_to_check as $file => $desc) {
    $full_path = ROOT_PATH . $file;
    if (file_exists($full_path)) {
        echo "✓ {$desc} 存在 ({$file})<br/>";
    } else {
        echo "✗ {$desc} 不存在 ({$file})<br/>";
    }
}

// 3. 检查数据库连接
echo "<h3>3. 检查数据库连接</h3>";

try {
    require_once(APP_PATH.'init.php');
    
    if (isset($DB) && is_object($DB)) {
        echo "✓ 数据库对象存在<br/>";
        
        // 测试数据库连接
        $table = $DB->table('options');
        $result = $DB->get_one("SELECT COUNT(*) as count FROM $table");
        echo "✓ 数据库连接正常<br/>";
        
    } else {
        echo "✗ 数据库对象不存在<br/>";
    }
} catch (Exception $e) {
    echo "✗ 数据库连接失败：" . $e->getMessage() . "<br/>";
}

// 4. 检查配置文件
echo "<h3>4. 检查配置文件</h3>";

$config_file = ROOT_PATH . 'data/static/options.php';
if (file_exists($config_file)) {
    echo "✓ 配置文件存在<br/>";
    
    // 尝试加载配置
    try {
        define('IN_HANFOX', true); // 兼容配置文件的安全检查
        require($config_file);
        if (isset($static_data) && is_array($static_data)) {
            echo "✓ 配置数据加载成功，包含 " . count($static_data) . " 项配置<br/>";
            
            // 检查必要的配置项
            $required_configs = array('site_url', 'site_name');
            foreach ($required_configs as $config_key) {
                if (isset($static_data[$config_key])) {
                    echo "✓ 配置项 {$config_key} 存在：" . $static_data[$config_key] . "<br/>";
                } else {
                    echo "⚠ 配置项 {$config_key} 不存在<br/>";
                }
            }
        } else {
            echo "✗ 配置数据格式错误<br/>";
        }
    } catch (Exception $e) {
        echo "✗ 配置文件加载失败：" . $e->getMessage() . "<br/>";
    }
} else {
    echo "✗ 配置文件不存在<br/>";
    echo "尝试生成配置文件...<br/>";
    
    // 尝试生成配置文件
    try {
        require_once(APP_PATH.'module/static.php');
        options_cache();
        echo "✓ 配置文件生成成功<br/>";
    } catch (Exception $e) {
        echo "✗ 配置文件生成失败：" . $e->getMessage() . "<br/>";
    }
}

// 5. 检查菜单是否已添加
echo "<h3>5. 检查后台菜单</h3>";

$admin_template = ROOT_PATH . 'themes/system/admin.html';
if (file_exists($admin_template)) {
    $content = file_get_contents($admin_template);
    if (strpos($content, 'search_push.php') !== false) {
        echo "✓ 后台菜单已添加<br/>";
    } else {
        echo "⚠ 后台菜单未添加<br/>";
        echo "请手动在 themes/system/admin.html 的辅助功能菜单中添加：<br/>";
        echo "<code>&lt;li&gt;&lt;a href=\"search_push.php\" target=\"main\"&gt;&lt;span class=\"file\"&gt;搜索引擎推送&lt;/span&gt;&lt;/a&gt;&lt;/li&gt;</code><br/>";
    }
} else {
    echo "✗ 后台模板文件不存在<br/>";
}

// 6. 检查PHP扩展
echo "<h3>6. 检查PHP扩展</h3>";

$required_extensions = array('curl', 'json', 'mbstring');
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ PHP扩展 {$ext} 已加载<br/>";
    } else {
        echo "✗ PHP扩展 {$ext} 未加载<br/>";
    }
}

// 7. 生成测试sitemap
echo "<h3>7. 生成测试网站地图</h3>";

try {
    if (isset($DB)) {
        require_once(APP_PATH.'module/search_push.php');
        
        // 加载配置
        if (file_exists($config_file)) {
            define('IN_HANFOX', true); // 兼容配置文件的安全检查
            require($config_file);
            $options = $static_data;
        }
        
        $xml = generate_sitemap_xml();
        
        if (file_exists(ROOT_PATH . 'sitemap.xml')) {
            echo "✓ 测试网站地图生成成功<br/>";
            echo "文件大小：" . filesize(ROOT_PATH . 'sitemap.xml') . " 字节<br/>";
        } else {
            echo "✗ 测试网站地图生成失败<br/>";
        }
    } else {
        echo "⚠ 跳过网站地图测试（数据库未连接）<br/>";
    }
} catch (Exception $e) {
    echo "✗ 网站地图生成失败：" . $e->getMessage() . "<br/>";
}

echo "<h3>修复完成</h3>";
echo "<p>如果发现任何错误，请根据提示进行修复。</p>";
echo "<p>修复完成后，您可以访问：<a href='system/search_push.php' target='_blank'>搜索引擎推送管理</a></p>";
echo "<p>或运行测试脚本：<a href='test_search_push.php' target='_blank'>功能测试</a></p>";
?>
